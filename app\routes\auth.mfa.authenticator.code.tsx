import type { ActionFunctionArgs, LoaderFunctionArgs, MetaFunction, TypedResponse } from "@remix-run/node";
import { json, redirect } from "@remix-run/node";
import { useSubmit } from "@remix-run/react";
import { useForm } from "react-hook-form";
import type { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button, Form, FormControl, FormField, FormItem, FormLabel, FormMessage, Input } from "@netpro/design-system";
import { Form as RemixForm } from "@remix-run/react/dist/components";
import type { JSX } from "react";
import { Method } from "~/lib/auth/utils/mfa";
import { commitSession, destroySession, getSession, getSessionData } from "~/lib/auth/utils/session.server";
import { mfaCodeSchema, type mfaCodeType, verifyMfaCode } from "~/features/mfa/api/verify-mfa-code";
import { middleware } from "~/lib/middlewares.server";

const title = "Authenticator code" as const;

export const meta: MetaFunction = () => [
  { title },
  { name: "Authenticator code page", content: title },
];

export async function loader({ request }: LoaderFunctionArgs): Promise<TypedResponse<never> | null> {
  // You should be redirected here if you just logged/configured MFA and have authenticator as a method
  await middleware(["auth"], request);

  const { mfaMethod, mfaCompleted } = await getSessionData(request);
  if (mfaMethod !== Method.Authenticator || mfaCompleted) {
    return redirect("/dashboard");
  }

  return null;
}

export async function action({ request }: ActionFunctionArgs): Promise<TypedResponse<null> | undefined> {
  const { userId, accessToken } = await middleware(["auth"], request);
  const { mfaMethod, mfaCompleted } = await getSessionData(request);
  if (mfaMethod !== Method.Authenticator || mfaCompleted) {
    // MFA method is not Authenticator, redirect to the dashboard
    return redirect("/dashboard");
  }

  const session = await getSession(request.headers.get("Cookie"));

  try {
    const formData = await request.formData();
    const data = JSON.parse(formData.get("data") as string) as mfaCodeType;
    mfaCodeSchema.parse(data);
    const verificationResult = await verifyMfaCode({ data: {
      code: data.code.trim(),
    }, accessToken, userId });

    if (verificationResult.success) {
      session.flash("notification", { title: "MFA code verified", message: "Welcome to the Private Client Portal", variant: "success" });
      session.set("mfaCompleted", true);

      return redirect("/dashboard", { headers: { "Set-Cookie": await commitSession(session) } });
    } else {
      const invalidAttempts = session.get("mfaAttempts") || 0;
      const currentInvalidAttempts = invalidAttempts + 1;
      session.set("mfaAttempts", currentInvalidAttempts);

      if (currentInvalidAttempts >= 5) {
        session.flash("notification", { title: "An error has occurred", message: "You have exceeded the number of attempts.", variant: "error" });

        return json(null, { status: 400, headers: { "Set-Cookie": await destroySession(session) } });
      }

      session.flash("notification", {
        title: "An error has occurred",
        message: `Invalid code, please try again. ${currentInvalidAttempts > 1 ? `You have ${5 - currentInvalidAttempts} attempts remaining before being logged out.` : ""}`,
        variant: "error",
      });

      return json(null, { status: 400, headers: { "Set-Cookie": await commitSession(session) } });
    }
  } catch (error) {
    const err = error as Response;
    const errorMessage = await err.text();

    if (errorMessage) {
      session.flash("notification", { title: "An error has occurred", message: errorMessage, variant: "error" });

      return json(null, { status: err.status, headers: { "Set-Cookie": await commitSession(session) } });
    }
  }
}

export default function MFAEmail(): JSX.Element {
  const submit = useSubmit();
  const form = useForm<z.infer<typeof mfaCodeSchema>>({
    resolver: zodResolver(mfaCodeSchema),
    defaultValues: {
      code: "",
    },
  });

  function onSubmit(data: z.infer<typeof mfaCodeSchema>): void {
    submit({ data: JSON.stringify(data) }, { method: "POST" });
  }

  return (
    <Form {...form}>
      <RemixForm onSubmit={form.handleSubmit(onSubmit)} noValidate>
        <FormField
          control={form.control}
          name="code"
          render={({ field, fieldState }) => (
            <FormItem>
              <FormLabel>Please type your authentication code:</FormLabel>
              <FormControl>
                <Input
                  invalid={!!fieldState.error}
                  placeholder="Authentication code"
                  className="rounded-none ring-0 border-b border-grey-500 h-8 hover:border-grey-700 focus:border-grey-700 hover:ring-0 focus:ring-0"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex flex-row justify-end w-full mt-3">
          <Button type="submit" className="rounded-none h-8 bg-[#0067b8] font-normal px-6">Submit</Button>
        </div>
      </RemixForm>
    </Form>
  )
}
