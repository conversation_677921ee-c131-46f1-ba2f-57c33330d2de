import { getSessionData } from "~/lib/auth/utils/session.server";
import type { MiddlewareProps, MiddlewareResponse } from "~/lib/middlewares.server";

export default async function requireBfrModule(
  { request }: MiddlewareProps,
): MiddlewareResponse<never | object> {
  const { companyModules } = await getSessionData(request);

  if (!companyModules?.basicFinancialReport.enabled) {
    // Status code 412: Precondition Failed
    throw new Response("Basic financial report module is not enabled for this company.", { status: 412 });
  }

  return {};
}
