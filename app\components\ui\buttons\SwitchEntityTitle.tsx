import type { JSX, ReactNode } from "react";

export type SwitchEntityTitleProps = {
  prefix?: string
  children: ReactNode
}

export function SwitchEntityTitle({ prefix, children }: SwitchEntityTitleProps): JSX.Element {
  return (
    <h4 className="text-white text-sm font-inter flex gap-1.5 overflow-hidden whitespace-nowrap pr-6">
      {prefix && (
        <span>
          {prefix}
          :
        </span>
      )}
      <span className="font-semibold overflow-hidden text-ellipsis">{children}</span>
    </h4>
  );
}
