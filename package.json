{"name": "tt-pcp-application", "type": "module", "version": "2.17.3", "private": true, "sideEffects": false, "engines": {"node": ">=20.14.0"}, "scripts": {"build": "remix vite:build", "dev": "remix vite:dev", "lint": "eslint --cache --cache-location ./node_modules/.cache/eslint .", "start": "remix-serve ./build/server/index.js", "typecheck": "tsc", "release": "commit-and-tag-version -t --tag-force", "test": "vitest", "test:e2e": "npx playwright test -- --browser all", "test:unit": "vitest --project unit", "test:unit:ci": "vitest run --project unit --reporter=default --reporter=junit --outputFile=test-results/junit-unit.xml", "test:component": "vitest --project component", "test:component:ci": "vitest run --project component --reporter=default --reporter=junit --outputFile=test-results/junit-component.xml", "test:vitest:ci": "vitest run --reporter=default --reporter=junit --outputFile=test-results/junit.xml", "generate-api-service": "npx cross-env NODE_TLS_REJECT_UNAUTHORIZED=0 npx @hey-api/openapi-ts -i https://localhost:7108/swagger/v1/swagger.json -o app/services/api-generated -c @hey-api/client-fetch"}, "dependencies": {"@azure/msal-node": "^2.13.0", "@hey-api/client-fetch": "^0.4.4", "@hookform/resolvers": "^3.9.0", "@netpro/auth": "^1.0.2", "@netpro/design-system": "^3.7.1", "@remix-run/node": "^2.12.0", "@remix-run/react": "^2.12.0", "@remix-run/serve": "^2.12.0", "date-fns": "^3.6.0", "flat": "^6.0.1", "isbot": "^4.1.0", "lucide-react": "^0.429.0", "mime": "^4.0.4", "qrcode": "^1.5.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.53.0", "zod": "^3.23.8"}, "devDependencies": {"@hey-api/openapi-ts": "^0.56.1", "@netpro/eslint-config": "^4.1.8", "@playwright/test": "^1.46.0", "@remix-run/dev": "^2.12.0", "@remix-run/testing": "^2.13.1", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.0.1", "@types/node": "^22.1.0", "@types/qrcode": "^1.5.5", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@vitest/browser": "^2.1.3", "@vitest/ui": "^2.1.3", "autoprefixer": "^10.4.19", "commit-and-tag-version": "^12.4.1", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc-b57d2823-20240822", "happy-dom": "^15.7.4", "playwright": "^1.48.0", "postcss": "^8.4.38", "remix-development-tools": "^4.7.7", "tailwindcss": "^3.4.4", "typescript": "^5.1.6", "vite": "^5.1.0", "vite-tsconfig-paths": "^4.2.1", "vitest": "^2.1.3"}, "commit-and-tag-version": {"releaseCommitMessageFormat": "chore(release): v{{currentTag}} see changelog for details [skip ci]", "commitUrlFormat": "https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/{{hash}}", "issueUrlFormat": "https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/{{id}}", "compareUrlFormat": "https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GT{{previousTag}}&targetVersion=GT{{currentTag}}&_a=files"}, "overrides": {"cookie": "^0.7.2"}}