import { zodResolver } from "@hookform/resolvers/zod";
import { Checkbox, Form, FormControl, FormField, FormItem, FormLabel, FormMessage, Input, PhonePrefix, Select, SelectContent, SelectItem, SelectTrigger, SelectValue, Textarea } from "@netpro/design-system";
import { Form as RemixForm, useFetcher, useNavigation } from "@remix-run/react";
import { type JSX, useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import type { FinalizeSchemaType } from "~/lib/basic-financial-report/types/finalize-schema";
import { EntityRelation, finalizeSchema } from "~/lib/basic-financial-report/types/finalize-schema";
import { BASIC_FINANCIAL_REPORT_FORM_ID } from "~/lib/basic-financial-report/utilities/constants";
import { Pages } from "~/lib/basic-financial-report/utilities/form-pages";
import { useSubmission } from "~/lib/submission/context/use-submission";
import { useScrollToError } from "~/lib/utilities/use-scroll-to-error";

export function Finalize(): JSX.Element {
  const { submissionData } = useSubmission();
  const data = useMemo(() => submissionData[Pages.FINALIZE] as FinalizeSchemaType, [submissionData]);
  const navigation = useNavigation()
  const isSubmitting = navigation.state === "submitting" || navigation.state === "loading"
  const form = useForm<FinalizeSchemaType>({
    resolver: zodResolver(finalizeSchema),
    shouldFocusError: false,
    defaultValues: {
      confirmationDeclaration: "false",
      confirmationAssets: "false",
      confirmationFunds: "false",
      confirmationReports: "false",
      confirmationTaxAdvice: "false",
      confirmationFeeAcknowledgement: "false",
      declarantName: "",
      address: "",
      otherEntityRelation: "",
      telephone: {
        countryCode: "",
        prefix: "",
        number: "",
      },
      email: "",
    },
  });
  const { reset, formState, setValue } = form;
  const [canFocus, setCanFocus] = useState(true)
  useScrollToError(formState, canFocus, setCanFocus);

  function onError(): void {
    setCanFocus(true)
  }

  useEffect(() => {
    setValue("confirmationDeclaration", "false")
    setValue("confirmationAssets", "false")
    setValue("confirmationFunds", "false")
    setValue("confirmationReports", "false")
    setValue("confirmationTaxAdvice", "false")
    setValue("confirmationFeeAcknowledgement", "false")
    reset(data, { keepDefaultValues: true });
  }, [data, reset, setValue]);

  const fetcher = useFetcher();

  function onSubmit(data: FinalizeSchemaType): void {
    fetcher.submit({ data: JSON.stringify(data) }, {
      method: "post",
    });
  }

  const watchEntityRelation = form.watch("entityRelation")

  return (
    <Form {...form}>
      <RemixForm onSubmit={form.handleSubmit(onSubmit, onError)} noValidate id={BASIC_FINANCIAL_REPORT_FORM_ID}>
        <p>Declaration and confirmation page</p>
        <div className="flex flex-col space-y-5 lg:w-1/2 sm:w-full py-5">
          <FormField
            control={form.control}
            name="confirmationDeclaration"
            render={({ field, fieldState }) => (
              <FormItem>
                <div className="flex flex-row items-top space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      invalid={!!fieldState.error}
                      onCheckedChange={value => field.onChange(String(value))}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormLabel className="font-normal">
                    The information provided and represented in this portal and any document/s uploaded in conjunction with completing this accounting portal is/are, to the best of my knowledge and belief, true and correct.*
                  </FormLabel>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="confirmationAssets"
            render={({ field, fieldState }) => (
              <FormItem>
                <div className="flex flex-row items-top space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      invalid={!!fieldState.error}
                      onCheckedChange={value => field.onChange(String(value))}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormLabel className="font-normal">
                    Aside from the represented and listed assets and liabilities in the form; no other assets and liabilities owned and incurred by the Company are in existence based on our knowledge.*
                  </FormLabel>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="confirmationFunds"
            render={({ field, fieldState }) => (
              <FormItem>
                <div className="flex flex-row items-top space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      invalid={!!fieldState.error}
                      onCheckedChange={value => field.onChange(String(value))}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormLabel className="font-normal">
                    The assets contributed to the Company and the funds used to pay for all Trident's services are from lawful sources.*
                  </FormLabel>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="confirmationReports"
            render={({ field, fieldState }) => (
              <FormItem>
                <div className="flex flex-row items-top space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      invalid={!!fieldState.error}
                      onCheckedChange={value => field.onChange(String(value))}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormLabel className="font-normal">
                    The Financial Reports prepared from this accounting portal are for client purposes only and no audit was conducted.*
                  </FormLabel>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="confirmationTaxAdvice"
            render={({ field, fieldState }) => (
              <FormItem>
                <div className="flex flex-row items-top space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      invalid={!!fieldState.error}
                      onCheckedChange={value => field.onChange(String(value))}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormLabel className="font-normal">
                    Any accounting treatment applied (and, in particular, the segregation of income) may not satisfy any regulatory or tax filing requirements. If any information contained herein is to be relied on for such filing, independent tax or legal advice should be obtained.*
                  </FormLabel>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="confirmationFeeAcknowledgement"
            render={({ field, fieldState }) => (
              <FormItem>
                <div className="flex flex-row items-top space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      invalid={!!fieldState.error}
                      onCheckedChange={value => field.onChange(String(value))}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormLabel className="font-normal">
                    I confirm and acknowledge that a submission fee in the amount of US$ 107.00 is due and payable in order to complete the submission process.*
                  </FormLabel>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <div className="flex-col space-y-2">
          <FormField
            control={form.control}
            name="declarantName"
            render={({ field, fieldState }) => (
              <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                <FormLabel>Name of person stating the declaration*</FormLabel>
                <FormControl>
                  <Input
                    invalid={!!fieldState.error}
                    {...field}
                    disabled={isSubmitting}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="address"
            render={({ field, fieldState }) => (
              <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                <FormLabel>Address*</FormLabel>
                <FormControl>
                  <Input
                    invalid={!!fieldState.error}
                    {...field}
                    disabled={isSubmitting}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="entityRelation"
            render={({ field, fieldState }) => (
              <FormItem>
                <FormLabel>Relation to entity*</FormLabel>
                <FormControl>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                    disabled={isSubmitting}
                  >
                    <FormControl className="lg:w-1/3 md:w-1/2 sm:w-full">
                      <SelectTrigger invalid={!!fieldState.error}>
                        <SelectValue placeholder="Select an option" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      { Object.values(EntityRelation).map(er => (
                        <SelectItem key={er} value={er}>
                          {er}
                        </SelectItem>
                      )) }
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          { watchEntityRelation === EntityRelation.OTHER && (
            <FormField
              control={form.control}
              name="otherEntityRelation"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel>Please specify*</FormLabel>
                  <FormControl className="md:w-1/2 sm:w-full">
                    <Textarea
                      invalid={!!fieldState.error}
                      {...field}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
          <FormField
            control={form.control}
            name="telephone"
            render={({ fieldState }) => (
              <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                <FormLabel>Telephone Number*</FormLabel>
                <div className="flex">
                  <FormField
                    control={form.control}
                    name="telephone.countryCode"
                    render={({ field }) => (
                      <FormControl>
                        <PhonePrefix
                          invalid={!!fieldState.error}
                          onChange={field.onChange}
                          onPrefixChange={value => form.setValue("telephone.prefix", value)}
                          className="min-w-20 max-w-fit rounded-r-none"
                          value={field.value}
                          disabled={isSubmitting}
                        />
                      </FormControl>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="telephone.number"
                    render={({ field }) => (
                      <FormControl>
                        <Input
                          className="rounded-l-none"
                          type="number"
                          invalid={!!fieldState.error}
                          {...field}
                          disabled={isSubmitting}
                        />
                      </FormControl>
                    )}
                  />
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="email"
            render={({ field, fieldState }) => (
              <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                <FormLabel>Email address*</FormLabel>
                <FormControl>
                  <Input
                    invalid={!!fieldState.error}
                    {...field}
                    disabled={isSubmitting}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </RemixForm>
    </Form>
  )
}
