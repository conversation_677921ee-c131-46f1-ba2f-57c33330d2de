import type { ActionFunctionArgs, LoaderFunctionArgs, MetaFunction, TypedResponse } from "@remix-run/node";
import { json, redirect, useLoaderData, useSubmit } from "@remix-run/react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import type { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Button,
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
} from "@netpro/design-system";
import { Form as RemixForm, useFetcher } from "@remix-run/react/dist/components";
import type { JSX } from "react"
import type { mfaCodeType } from "~/features/mfa/api/verify-mfa-code";
import { mfaCodeSchema, verifyMfaCode } from "~/features/mfa/api/verify-mfa-code";
import { Method } from "~/lib/auth/utils/mfa";
import { commitSession, destroySession, getSession, getSessionData } from "~/lib/auth/utils/session.server";
import { useCountdown } from "~/lib/utilities/use-countdown";
import { middleware } from "~/lib/middlewares.server";
import { useDelayedState } from "~/lib/utilities/use-delayed-state";

const title = "Email code" as const;

export const meta: MetaFunction = () => [
  { title },
  { name: "Email code page", content: title },
];

export async function loader({ request }: LoaderFunctionArgs): Promise<TypedResponse<{
  mfaEmailCodeExpiresAt: string | null
}>> {
  await middleware(["auth"], request);
  const { mfaMethod, mfaCompleted, mfaEmailCodeExpiresAt } = await getSessionData(request);
  if (mfaMethod !== Method.Email || mfaCompleted) {
    // MFA method is not Email, redirect to the dashboard
    return redirect("/dashboard");
  }

  // If the email code is still valid, return the expiration date to avoid sending a new code
  if (mfaEmailCodeExpiresAt && new Date(mfaEmailCodeExpiresAt) > new Date()) {
    return json({ mfaEmailCodeExpiresAt });
  }

  return json({ mfaEmailCodeExpiresAt: null });
}

export async function action({ request }: ActionFunctionArgs): Promise<TypedResponse<null> | undefined> {
  const { userId, accessToken } = await middleware(["auth"], request);
  const { mfaMethod, mfaCompleted } = await getSessionData(request);
  if (mfaMethod !== Method.Email || mfaCompleted) {
    // MFA method is not Email, redirect to the dashboard
    return redirect("/dashboard");
  }

  const session = await getSession(request.headers.get("Cookie"));

  try {
    const formData = await request.formData();
    const data = JSON.parse(formData.get("data") as string) as mfaCodeType;
    mfaCodeSchema.parse(data);
    const verificationResult = await verifyMfaCode({ data: {
      code: data.code.trim(),
    }, accessToken, userId });

    if (verificationResult.success) {
      session.flash("notification", { title: "MFA code verified", message: "Welcome to the Private Client Portal", variant: "success" });
      session.set("mfaCompleted", true);

      return redirect("/dashboard", { headers: { "Set-Cookie": await commitSession(session) } });
    } else {
      const invalidAttempts = session.get("mfaAttempts") || 0;
      const currentInvalidAttempts = invalidAttempts + 1;
      session.set("mfaAttempts", currentInvalidAttempts);

      if (currentInvalidAttempts >= 5) {
        session.flash("notification", { title: "An error has occurred", message: "You have exceeded the number of attempts.", variant: "error" });

        return json(null, { status: 400, headers: { "Set-Cookie": await destroySession(session) } });
      }

      session.flash("notification", {
        title: "An error has occurred",
        message: `Invalid code, please try again. ${currentInvalidAttempts > 1 ? `You have ${5 - currentInvalidAttempts} attempts remaining before being logged out.` : ""}`,
        variant: "error",
      });

      return json(null, { status: 400, headers: { "Set-Cookie": await commitSession(session) } });
    }
  } catch (error) {
    const err = error as Response;
    const errorMessage = await err.text();

    if (errorMessage) {
      session.flash("notification", { title: "An error has occurred", message: errorMessage, variant: "error" });

      return json(null, { status: err.status, headers: { "Set-Cookie": await commitSession(session) } });
    }
  }
}

export default function MFAEmail(): JSX.Element {
  const { mfaEmailCodeExpiresAt: initialExpiresValue } = useLoaderData<typeof loader>();
  const [expiresValue, setExpiresValue] = useState(initialExpiresValue);
  const isLoading = useDelayedState(expiresValue === null, 800);
  const { submit: submitFetcher, state: fetcherState, data: fetcherData } = useFetcher<{ mfaEmailCodeExpiresAt: string }>();

  useEffect(() => {
    if (!expiresValue) {
      submitFetcher(
        {},
        { method: "post", action: "/api/mfa/email/send-code" },
      );
    }
  }, [expiresValue, submitFetcher]);

  useEffect(() => {
    if (fetcherData && fetcherState === "idle") {
      setExpiresValue(fetcherData.mfaEmailCodeExpiresAt);
    }
  }, [fetcherData, fetcherState]);

  const mfaEmailCodeExpiresAt = new Date(expiresValue || new Date());
  const { minutes, seconds } = useCountdown(mfaEmailCodeExpiresAt);
  const [expiryMessage, setExpiryMessage] = useState("");
  const submit = useSubmit();
  const form = useForm<z.infer<typeof mfaCodeSchema>>({
    resolver: zodResolver(mfaCodeSchema),
    defaultValues: {
      code: "",
    },
  });

  function onSubmit(data: z.infer<typeof mfaCodeSchema>): void {
    submit({ data: JSON.stringify(data) }, { method: "POST" });
  }

  function handleResendEmail(): void {
    submitFetcher(
      {},
      { method: "post", action: "/api/mfa/email/send-code" },
    );
  }

  useEffect(() => {
    if (isLoading) {
      setExpiryMessage("Sending email...");

      return;
    }

    if (minutes + seconds > 0) {
      setExpiryMessage(`The code will expire in ${minutes}:${seconds.toString().padStart(2, "0")}`);
    } else {
      setExpiryMessage("The code has expired. Please request a new one.");
    }
  }, [minutes, seconds, isLoading]);

  return (
    <Form {...form}>
      <RemixForm onSubmit={form.handleSubmit(onSubmit)} noValidate>
        <FormField
          control={form.control}
          name="code"
          render={({ field, fieldState }) => (
            <FormItem>
              <FormLabel>Please type your email authentication code:</FormLabel>
              <FormControl>
                <Input
                  invalid={!!fieldState.error}
                  disabled={minutes + seconds <= 0 || isLoading}
                  placeholder="Authentication code"
                  className="rounded-none ring-0 border-b border-grey-500 h-8 hover:border-grey-700 focus:border-grey-700 hover:ring-0 focus:ring-0"
                  {...field}
                />
              </FormControl>
              <FormDescription>{expiryMessage}</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex flex-row justify-end w-full mt-3">
          {!isLoading && minutes + seconds <= 0 && (
            <Button onClick={handleResendEmail} type="button" className="rounded-none h-8 bg-[#0067b8] font-normal px-6 mr-2">Re-send email</Button>
          )}
          <Button type="submit" className="rounded-none h-8 bg-[#0067b8] font-normal px-6" disabled={isLoading}>Submit</Button>
        </div>
      </RemixForm>
    </Form>
  )
}
