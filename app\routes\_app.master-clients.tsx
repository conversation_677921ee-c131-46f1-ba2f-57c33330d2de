import { Alert } from "@netpro/design-system";
import type { LoaderFunctionArgs, TypedResponse } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useFetcher, useLoaderData } from "@remix-run/react";
import type { ReactNode } from "react";
import MasterClientsSearch from "~/features/master-clients/components/MasterClientsSearch";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { middleware } from "~/lib/middlewares.server";
import type { MasterClientsSearchResultDTO, MasterClientsSearchResultsDTO } from "~/services/api-generated";
import { clientGetMasterClients } from "~/services/api-generated";

export async function loader({ request }: LoaderFunctionArgs): Promise<TypedResponse<MasterClientsSearchResultsDTO | never >> {
  await middleware(["auth", "mfa", "terms"], request);
  const { data: masterClients, error } = await clientGetMasterClients({ headers: await authHeaders(request) });

  if (error) {
    throw new Response("Master Client list could currently not be retrieved from the server.", { status: 500 });
  }

  return json(masterClients);
}

export default function MasterClients(): ReactNode {
  const { masterClients } = useLoaderData<typeof loader>();
  const fetcher = useFetcher<any>();
  const handleSelectMasterClient = (selectedMasterClient: MasterClientsSearchResultDTO): void => {
    fetcher.submit(
      {
        id: selectedMasterClient.masterClientId as string,
        actionType: "select",
      },
      { method: "post", action: "/api/master-clients" },
    );
  }

  return (
    <div className="flex flex-col gap-10 w-full max-w-3xl">
      <Alert title="Multiple Master Client Codes" dismissible>
        <p>
          Your account contains multiple Master Client Codes. Please select the one to work with below.
          <br />
          You can change the selection later.
        </p>
      </Alert>

      <MasterClientsSearch
        initialValues={masterClients as MasterClientsSearchResultDTO[]}
        onSelect={handleSelectMasterClient}
      />
    </div>
  );
}
