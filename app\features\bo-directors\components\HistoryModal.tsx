import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
} from "@netpro/design-system";
import { useFetcher, useLocation } from "@remix-run/react";
import type { JSX } from "react";
import { useEffect, useRef, useState } from "react";
import type { BeneficialOwnerComparisonDTO, BeneficialOwnerDTO, DirectorComparisonDTO, DirectorDTO, LegalEntityRelationUpdateRequestType } from "~/services/api-generated";

type HistoryModalProps = {
  open: boolean
  handleOpenChange: (isOpen: boolean) => void
  selectedItem: DirectorDTO | BeneficialOwnerDTO
};

type ComparisonData = BeneficialOwnerComparisonDTO | DirectorComparisonDTO;

type FetcherData = {
  comparisonData: ComparisonData
};

const updateRequestType: Record<LegalEntityRelationUpdateRequestType, string> = {
  MissingBeneficialOwners: "Missing Beneficial Owners",
  MissingDirectors: "Missing Directors",
  MissingShareholders: "Missing Shareholders",
  ChangeOfBeneficialOwners: "Change of Beneficial Owners",
  ChangeOfBeneficialOwnersAddress: "Change of Beneficial Owners Address",
  ChangeOfBeneficialOwnersParticulars: "Change of Beneficial Owners Particulars",
  ChangeOfDirectors: "Change of Directors",
  ChangeOfDirectorsAddress: "Change of Directors Address",
  ChangeOfDirectorsParticulars: "Change of Directors Particulars",
  ChangeOfShareholders: "Change of Shareholders",
  ChangeOfShareholdersAddress: "Change of Shareholders Address",
  ChangeOfShareholdersParticulars: "Change of Shareholders Particulars",
  OtherUpdateOfBeneficialOwners: "Other Update of Beneficial Owners",
  OtherUpdateOfDirectors: "Other Update of Directors",
  OtherUpdateOfShareholders: "Other Update of Shareholders",
};

export default function HistoryModal({
  open,
  handleOpenChange,
  selectedItem,
}: HistoryModalProps): JSX.Element {
  const [isLoading, setIsLoading] = useState(false);
  const location = useLocation()
  const isBeneficialOwner = location.pathname.includes("beneficial-owner")
  const fetcher = useFetcher<FetcherData>();
  const hasFetchedRef = useRef(false);

  useEffect(() => {
    if (open && selectedItem && selectedItem.id && !hasFetchedRef.current) {
      const path = `/api/bo-directors/${isBeneficialOwner ? "beneficial-owner" : "director"}/${selectedItem.id}/comparison`;

      fetcher.load(path);
      hasFetchedRef.current = true;
    }

    if (!open) {
      hasFetchedRef.current = false;
    }
  }, [open, selectedItem, isBeneficialOwner, fetcher]);

  useEffect(() => {
    setIsLoading(fetcher.state === "loading");
  }, [fetcher.state]);

  const comparisonData: ComparisonData | undefined = fetcher.data?.comparisonData;

  return (
    <Dialog modal open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-3xl [&>button]:hidden">
        <DialogHeader>
          <DialogDescription>
            Request for update
          </DialogDescription>
          <DialogTitle className="text-2xl font-semibold">
            Latest request for update details
          </DialogTitle>
        </DialogHeader>
        <Table className="mt-4">
          <TableBody>
            <TableRow className="border-t border-gray-200">
              <TableHead className="font-bold text-black"> Type of Request </TableHead>
              <TableCell>{comparisonData?.currentVersion?.metaData?.updateRequestTypeName ? updateRequestType[comparisonData?.currentVersion?.metaData?.updateRequestTypeName as LegalEntityRelationUpdateRequestType] : "N/A"}</TableCell>
            </TableRow>
            <TableRow>
              <TableHead className="font-bold text-black"> Additional Information </TableHead>
              <TableCell>{comparisonData?.currentVersion?.metaData?.updateRequestComments || "N/A"}</TableCell>
            </TableRow>
          </TableBody>
        </Table>

        <DialogFooter className="flex items-center gap-4">
          {isLoading
            ? (
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <p>Loading ...</p>
                </div>
              )
            : null}
          <Button
            type="button"
            size="sm"
            variant="outline"
            onClick={() => handleOpenChange(false)}
          >
            <span className="text-xs font-semibold">Close</span>
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
