import type { ActionFunctionArgs, TypedResponse } from "@remix-run/node";
import { json, redirect } from "@remix-run/react";
import { sendMfaEmail } from "~/features/mfa/api/send-mfa-email";
import { Method } from "~/lib/auth/utils/mfa";
import { commitSession, getSession, getSessionData } from "~/lib/auth/utils/session.server";
import { middleware } from "~/lib/middlewares.server";

export async function action({ request }: ActionFunctionArgs): Promise<TypedResponse<{
  mfaEmailCodeExpiresAt: string
}>> {
  const { userId, accessToken } = await middleware(["auth"], request);
  const { mfaMethod, mfaCompleted, mfaEmailCodeExpiresAt } = await getSessionData(request);
  const session = await getSession(request.headers.get("Cookie"));
  if (mfaMethod !== Method.Email || mfaCompleted) {
    // MFA method is not Email, redirect to the dashboard
    return redirect("/dashboard");
  }

  // If the email code is still valid, return the expiration date to avoid sending a new code
  if (mfaEmailCodeExpiresAt && new Date(mfaEmailCodeExpiresAt) > new Date()) {
    return json({ mfaEmailCodeExpiresAt });
  }

  const { mfaEmailCodeExpiresAt: newMfaEmailCodeExpiresAt } = await sendMfaEmail({ accessToken, userId });
  session.set("mfaEmailCodeExpiresAt", newMfaEmailCodeExpiresAt);

  return json({ mfaEmailCodeExpiresAt: newMfaEmailCodeExpiresAt }, { headers: { "Set-Cookie": await commitSession(session) } });
}
