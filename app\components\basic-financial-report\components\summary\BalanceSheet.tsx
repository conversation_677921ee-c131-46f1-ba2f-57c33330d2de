import { useLoaderData } from "@remix-run/react";
import { getYear } from "date-fns";
import type { ReactNode } from "react";
import { useBalanceSheetCalculation } from "~/lib/basic-financial-report/hooks/report/balance-sheet/use-calculation";
import { transformNumberToCurrency } from "~/lib/basic-financial-report/utilities/currencies";
import { Pages } from "~/lib/basic-financial-report/utilities/form-pages";
import { formatDate } from "~/lib/utilities/format";
import type { BasicFinancialReportSummaryLoader } from "~/routes/_pdf.basic-financial-report.$id.summary";

export function BalanceSheet(): ReactNode {
  const { submissionData } = useLoaderData<BasicFinancialReportSummaryLoader>()
  const endFiscalYear = submissionData[Pages.FINANCIAL_PERIOD].endFiscalYear
  const endDate = formatDate(endFiscalYear);
  const {
    intangibleAssets,
    fixedAssets,
    totalFixedAssets,
    investments,
    bankCashBalances,
    otherCurrentAssets,
    totalCurrentAssets,
    loansDueWithinOneYear,
    dueToFromShareholder,
    accountPayableAccrual,
    otherLiabilitiesDueWithinOneYear,
    totalCurrentLiabilities,
    totalAssetsLessCurrentLiabilities,
    loansDueAfterOneYear,
    otherLiabilitiesDueAfterOneYear,
    totalNonCurrentLiabilities,
    netAssets,
    shareCapital,
    profitAndLossAccount,
    otherShareholderReserves,
    totalEquity,
  } = useBalanceSheetCalculation(submissionData)

  return (
    <div className="text-xs leading-tight">
      {/* Header */}
      <div className="text-center mb-4">
        <h2 className="text-base mb-1">{`FINANCIAL SUMMARY FOR FINANCIAL YEAR ENDING ${getYear(endFiscalYear)}`}</h2>
        <h3 className="text-sm font-bold">{`SUMMARY OF ASSETS AND LIABILITIES AS AT ${endDate}`}</h3>
      </div>

      {/* Financial Statement Table */}
      <table className="w-full border-collapse">
        <tbody>
          <tr>
            <td className="font-bold underline pt-2">ASSETS</td>
            <td className="text-right font-bold pt-2">USD</td>
            <td className="text-right font-bold pt-2">USD</td>
          </tr>
          <tr>
            <td className="font-bold italic pt-3 pb-2">NON-CURRENT ASSETS</td>
            <td></td>
            <td></td>
          </tr>
          <tr>
            <td>INTANGIBLE ASSETS</td>
            <td className="text-right">{transformNumberToCurrency(intangibleAssets)}</td>
            <td></td>
          </tr>
          <tr>
            <td className="pb-2">FIXED ASSETS</td>
            <td className="text-right border-b border-black pb-2">{transformNumberToCurrency(fixedAssets)}</td>
            <td></td>
          </tr>
          <tr>
            <td className="italic font-bold pb-5">TOTAL FIXED ASSETS</td>
            <td></td>
            <td className="text-right pb-5">{transformNumberToCurrency(totalFixedAssets)}</td>
          </tr>
          <tr>
            <td className="font-bold italic pb-2">CURRENT ASSETS</td>
            <td></td>
            <td></td>
          </tr>
          <tr>
            <td>INVESTMENTS</td>
            <td className="text-right">{transformNumberToCurrency(investments)}</td>
            <td></td>
          </tr>
          <tr>
            <td>BANK / CASH BALANCES</td>
            <td className="text-right">{transformNumberToCurrency(bankCashBalances)}</td>
            <td></td>
          </tr>
          <tr>
            <td className="pb-2">OTHER CURRENT ASSETS</td>
            <td className="text-right border-b border-black pb-2">{transformNumberToCurrency(otherCurrentAssets)}</td>
            <td></td>
          </tr>
          <tr>
            <td className="italic font-bold">TOTAL CURRENT ASSETS</td>
            <td></td>
            <td className="text-right">{transformNumberToCurrency(totalCurrentAssets)}</td>
          </tr>
          <tr>
            <td className="font-bold underline pt-5" colSpan={3}>
              CREDITORS: AMOUNTS FALLING DUE WITHIN 1 YEAR
            </td>
          </tr>
          <tr>
            <td className="pt-2">LOANS</td>
            <td className="text-right pt-2">{transformNumberToCurrency(loansDueWithinOneYear)}</td>
            <td></td>
          </tr>
          <tr>
            <td>DUE TO/(FROM) SHAREHOLDER</td>
            <td className="text-right">{transformNumberToCurrency(dueToFromShareholder)}</td>
            <td></td>
          </tr>
          <tr>
            <td>ACCOUNTS PAYABLE AND ACCRUALS</td>
            <td className="text-right">{transformNumberToCurrency(accountPayableAccrual)}</td>
            <td></td>
          </tr>
          <tr>
            <td className="pb-2">OTHER LIABILITIES</td>
            <td className="text-right border-b border-black pb-2">{transformNumberToCurrency(otherLiabilitiesDueWithinOneYear)}</td>
            <td></td>
          </tr>
          <tr>
            <td className="italic font-bold pb-2">TOTAL CURRENT LIABILITIES</td>
            <td></td>
            <td className="text-right pb-2">{transformNumberToCurrency(totalCurrentLiabilities)}</td>
          </tr>
          <tr>
            <td className="italic font-bold pb-5">TOTAL ASSETS LESS CURRENT LIABILITIES</td>
            <td></td>
            <td className="text-right border-t border-black pb-5">{transformNumberToCurrency(totalAssetsLessCurrentLiabilities)}</td>
          </tr>
          <tr>
            <td className="font-bold pt-3" colSpan={3}>
              CREDITORS: AMOUNTS FALLING DUE AFTER MORE THAN 1 YEAR
            </td>
          </tr>
          <tr>
            <td className="pt-2">LOANS</td>
            <td className="text-right pt-2">{transformNumberToCurrency(loansDueAfterOneYear)}</td>
            <td></td>
          </tr>
          <tr>
            <td className="pb-2">OTHER LIABILITIES</td>
            <td className="text-right border-b border-black pb-2">{transformNumberToCurrency(otherLiabilitiesDueAfterOneYear)}</td>
            <td></td>
          </tr>
          <tr>
            <td className="italic font-bold">TOTAL NON-CURRENT LIABILITIES</td>
            <td></td>
            <td className="text-right border-b border-black">{transformNumberToCurrency(totalNonCurrentLiabilities)}</td>
          </tr>
          <tr>
            <td className="font-bold py-5">NET ASSETS</td>
            <td></td>
            <td className="text-right border-b border-black py-5">{transformNumberToCurrency(netAssets)}</td>
          </tr>
          <tr>
            <td className="font-bold">CAPITAL AND RESERVES</td>
            <td></td>
            <td></td>
          </tr>
          <tr>
            <td className="pt-2">SHARE CAPITAL</td>
            <td></td>
            <td className="text-right pt-2">{transformNumberToCurrency(shareCapital)}</td>
          </tr>
          <tr>
            <td>PROFIT AND LOSS ACCOUNT</td>
            <td></td>
            <td className="text-right">{transformNumberToCurrency(profitAndLossAccount)}</td>
          </tr>
          <tr>
            <td>OTHER SHAREHOLDER RESERVES</td>
            <td></td>
            <td className="text-right border-b border-black">{transformNumberToCurrency(otherShareholderReserves)}</td>
          </tr>
          <tr>
            <td className="italic pt-2 font-bold ">TOTAL EQUITY</td>
            <td></td>
            <td className="text-right border-b border-black pt-2">{transformNumberToCurrency(totalEquity)}</td>
          </tr>
        </tbody>
      </table>
    </div>
  )
}
