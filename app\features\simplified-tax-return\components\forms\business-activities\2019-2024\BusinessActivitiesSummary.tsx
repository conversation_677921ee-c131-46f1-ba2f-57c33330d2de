import { Table, TableBody, Table<PERSON>ell, TableHead, TableHeader, TableRow } from "@netpro/design-system";
import type { JSX } from "react";
import { type BusinessActivitiesFormType, BusinessActivity } from "~/lib/simplified-tax-return/types/business-activity/2019-2024/business-activity-schema";
import { Pages } from "~/lib/simplified-tax-return/utilities/form-pages";
import { useSubmission } from "~/lib/submission/context/use-submission";
import { formatDate } from "~/lib/utilities/format";

export function BusinessActivitiesSummary(): JSX.Element {
  const { submissionData } = useSubmission();
  const businessActivities = submissionData[Pages.BUSINESS_ACTIVITIES] as BusinessActivitiesFormType;

  return (
    <section id="business-activities-section">
      <h2 className="text-lg font-semibold">Business Activities</h2>
      <Table className="border border-blue-600 pointer-events-none mb-2">
        <TableHeader>
          <TableRow className="text-center">
            <TableHead className="w-[20%] text-center font-semibold text-black border border-blue-600 py-1 ">From</TableHead>
            <TableHead className="w-[20%] text-center font-semibold text-black border border-blue-600 py-1 ">To</TableHead>
            <TableHead className="w-[20%] font-semibold text-black border border-blue-600 py-1 ">Activity</TableHead>
            <TableHead className="w-[40%] font-semibold text-black border border-blue-600 py-1 ">Activity Type</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {businessActivities?.activities?.map(activity => (
            <TableRow key={activity.activity} className="">
              <TableCell className="text-center border border-blue-600 py-1">
                <span>{formatDate(activity.from as Date)}</span>
              </TableCell>
              <TableCell className="text-center border border-blue-600 py-1">
                <span>{formatDate(activity.to as Date)}</span>
              </TableCell>
              <TableCell className="border border-blue-600 py-1">
                <span>{activity.type}</span>
              </TableCell>
              <TableCell className="border border-blue-600 py-1">
                <span>{activity.activity === BusinessActivity.OTHER ? activity.otherActivity : activity.activity}</span>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </section>
  );
}
