import type { LoaderFunctionArgs, TypedResponse } from "@remix-run/node";
import { redirect } from "@remix-run/react";
import { requireActiveModule } from "~/features/modules/api/get-modules";
import { Modules } from "~/lib/utilities/modules";
import { middleware } from "~/lib/middlewares.server";
import { clientCreateSubmission } from "~/services/api-generated";
import { Pages } from "~/lib/basic-financial-report/utilities/form-pages";
import { commitSession, getSession } from "~/lib/auth/utils/session.server";
import { authHeaders } from "~/lib/auth/utils/auth-headers";

export async function loader({ request }: LoaderFunctionArgs): Promise<TypedResponse<never>> {
  const session = await getSession(request.headers.get("Cookie"));
  const { company } = await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany", "requireBfrModule"], request);
  const { module } = await requireActiveModule({ request, key: Modules.BASIC_FINANCIAL_REPORT, companyId: company.companyId });
  const { data: newSubmission, error } = await clientCreateSubmission({
    headers: await authHeaders(request),
    path: {
      companyId: company.companyId,
      moduleId: module.id as string,
    },
  });

  if (error) {
    session.flash("notification", { title: "Error!", message: error.exceptionMessage, variant: "error" });

    return redirect("/basic-financial-report/drafts", {
      headers: { "Set-Cookie": await commitSession(session) },
    });
  }

  if (!newSubmission) {
    throw new Response("Submission not found", { status: 404 })
  }

  return redirect(`/basic-financial-report/${newSubmission.id}/${Pages.FINANCIAL_PERIOD}`);
}
