import { BoDirTable } from "./BoDirTable";
import { DirectorColumns } from "~/lib/bo-directors/utilities/bo-directors-columns";
import type { DirectorType } from "~/lib/bo-directors/utilities/bo-directors-columns";
import type { DirectorDTO } from "~/services/api-generated";

type DirectorTableProps = {
  title: string
  items: DirectorDTO[]
  type: DirectorType
  requiredFields: string[]
  visibleColumns: string[]
};

export default function DirectorTable({
  title,
  items,
  type,
  requiredFields,
  visibleColumns,
}: DirectorTableProps) {
  return (
    <BoDirTable<DirectorDTO, DirectorType>
      title={title}
      items={items}
      type={type}
      requiredFields={requiredFields}
      visibleColumns={visibleColumns}
      columnsMap={DirectorColumns}
      sortFieldKeys={["name", "officerTypeName"]}
      keyPrefix="director"
    />
  );
}
