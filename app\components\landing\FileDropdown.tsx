import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@netpro/design-system"

type FileDropdownProps = {
  title: string
  items: { name: string, href: string }[]
}

export function FileDropdown({ title, items }: FileDropdownProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <span
          className="flex items-center gap-2 font-plaak-bold tracking-widest group text-sm cursor-pointer"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="11.122" height="11.294" viewBox="0 0 11.122 11.294" className="mr-2 group-hover:rotate-180">
            <path id="Path_356" data-name="Path 356" d="M26.1,37.409,37.222,26.116H26.1Z" transform="translate(-26.1 -26.115)" fill="#007aff"></path>
          </svg>
          {title}
        </span>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="min-w-56 font-plaak-regular bg-white border-none rounded-none py-0 gap-y-[1px] flex flex-col tracking-widest">
        {items.map(item => (
          <DropdownMenuItem key={item.name} asChild className="bg-[#DBFE87] focus:bg-[#CAFE4F] py-3"><a target="_blank" href={item.href}>{item.name}</a></DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
