import { client } from "~/lib/api-client";
import type { ClientRequestHeaders } from "~/lib/types/client-request-headers";

export type Submission = {
  dataSet: Record<string, string>
}

type UpdateSubmissionParams = {
  id: string
  submission: Submission
}

export function updateSubmissionDataset({ id, submission, accessToken, userId }: UpdateSubmissionParams & ClientRequestHeaders): Promise<Submission> {
  return client.put<Submission>(
    `/client/submissions/${id}/dataset`,
    accessToken,
    userId,
    submission,
  );
}
