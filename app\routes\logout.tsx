import { type ActionFunctionArgs, type TypedResponse, redirect } from "@remix-run/node";
import { destroySession, getSession } from "~/lib/auth/utils/session.server";

export async function loader(): Promise<TypedResponse> {
  throw new Response("Invalid method", { status: 405 });
}

export async function action({ request }: ActionFunctionArgs): Promise<TypedResponse> {
  const session = await getSession(request.headers.get("<PERSON>ie"));

  return redirect("/", {
    headers: { "Set-Cookie": await destroySession(session) },
  });
}
