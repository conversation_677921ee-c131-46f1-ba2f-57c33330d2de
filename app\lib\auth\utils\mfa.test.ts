import {
  describe,
  expect,
  it,
} from "vitest";
import type { MethodKeys } from "./mfa";
import { Method } from "./mfa";

describe("mfa.ts", () => {
  it("should export Method", () => {
    const expectedValues: MethodKeys[] = ["email-code", "authenticator", ""];
    const expectedKeys = ["Email", "Authenticator", "None"];
    expect(Object.values(Method)).toEqual(expectedValues);
    expect(Object.keys(Method)).toEqual(expectedKeys);
  });
});
