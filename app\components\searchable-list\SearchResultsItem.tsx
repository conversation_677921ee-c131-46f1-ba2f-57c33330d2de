import { Badge, But<PERSON> } from "@netpro/design-system";
import type { JSX } from "react";
import type { SearchResultsItemDTO } from "~/lib/types/search-results-type";

type SearchResultsItemProps = {
  item: SearchResultsItemDTO
  onSelect: (itemId: string) => void
}

export function SearchResultsItem({
  item,
  onSelect,
}: SearchResultsItemProps): JSX.Element {
  return (
    <div
      className="group w-full transition-all duration-200 flex justify-between items-center py-2.5 px-5 rounded bg-white hover:ring-primary hover:ring-inset hover:ring-2 cursor-pointer"
      onClick={() => onSelect(item.id)}
    >
      <div className="w-full flex flex-col">
        <span className="text-lg font-semibold font-inter">
          {item.title}
        </span>
        <span className="text-sm font-normal text-gray-500">{item.subtitle}</span>
      </div>
      <div className="flex items-center gap-2">
        {item.isActive === false && (<Badge variant="secondary" className="bg-teal-100">Inactive</Badge>)}
        {item.isClosing && (<Badge variant="secondary" className="bg-teal-100">Closing</Badge>)}
      </div>
      <Button variant="link">Select</Button>
    </div>
  );
}
