import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>alog<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogHeader,
  DialogTitle,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  ScrollArea,
  ScrollBar,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@netpro/design-system";
import type { ReactNode } from "react";
import type { UseFormReturn } from "react-hook-form";
import { Form as RemixForm } from "@remix-run/react"
import { eachYearOfInterval, endOfYear, startOfYear } from "date-fns";
import { CurrencyInput } from "~/components/ui/inputs/CurrencyInput";
import { Currency } from "~/lib/basic-financial-report/utilities/currencies";
import type { AssetSchemaType } from "~/lib/basic-financial-report/types/non-current-assets-details-schema";

type Props = {
  open: boolean
  setOpen: (open: boolean) => void
  onSubmit: (data: AssetSchemaType) => void
  form: UseFormReturn<AssetSchemaType>
};

const FORM_ID = "asset-dialog-form"
const currentYear = new Date().getFullYear();
const years = eachYearOfInterval({
  start: startOfYear(new Date(1900, 0, 1)), // January 1, 1900
  end: endOfYear(new Date(currentYear, 11, 31)), // December 31 of the current year
}).map(date => date.getFullYear()).reverse();

export function AssetDialog({
  open,
  setOpen,
  onSubmit,
  form,
}: Props): ReactNode {
  return (
    <Dialog open={open} onOpenChange={setOpen} modal>
      <DialogContent
        className="max-w-screen-sm"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <ScrollArea className="pr-3">
          <Form {...form}>
            <RemixForm onSubmit={form.handleSubmit(onSubmit)} className="p-2" noValidate id={FORM_ID}>
              <DialogHeader>
                <DialogTitle>Asset</DialogTitle>
              </DialogHeader>
              <div className="flex-col space-y-2 pt-4">
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field, fieldState }) => (
                    <FormItem className="md:w-1/2 sm:w-full">
                      <FormLabel>Description*</FormLabel>
                      <FormControl>
                        <Input
                          invalid={!!fieldState.error}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="purchaseYear"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormLabel>
                        <p>
                          Purchase Year*
                        </p>
                      </FormLabel>
                      <FormControl>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value}
                          disabled={field.disabled}
                        >
                          <FormControl className="md:w-1/2 sm:w-full">
                            <SelectTrigger invalid={!!fieldState.error}>
                              <SelectValue placeholder="Select an option" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            { years.map(year => (
                              <SelectItem key={year} value={year.toString()}>
                                {year}
                              </SelectItem>
                            )) }
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="purchaseCost"
                  render={({ field, fieldState }) => (
                    <FormItem className="md:w-1/2 sm:w-full">
                      <FormLabel>Purchase Cost*</FormLabel>
                      <FormControl>
                        <CurrencyInput
                          currencyName={Currency.USD}
                          invalid={!!fieldState.error}
                          {...field}
                          type="number"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="assessedValue"
                  render={({ field, fieldState }) => (
                    <FormItem className="md:w-1/2 sm:w-full">
                      <FormLabel>Assessed Value*</FormLabel>
                      <FormControl>
                        <CurrencyInput
                          currencyName={Currency.USD}
                          invalid={!!fieldState.error}
                          {...field}
                          type="number"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <DialogFooter className="pt-4">
                <div className="flex w-full justify-end">
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline" onClick={() => setOpen(false)} type="button">Cancel</Button>
                    <Button size="sm" variant="default" type="submit" form={FORM_ID}>Save</Button>
                  </div>
                </div>
              </DialogFooter>
            </RemixForm>
          </Form>
          <ScrollBar />
        </ScrollArea>
      </DialogContent>
    </Dialog>
  )
}
