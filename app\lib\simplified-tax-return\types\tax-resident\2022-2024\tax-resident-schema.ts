import { z } from "zod";
import { stringBoolean } from "~/lib/utilities/zod-validators";

export const taxResidentSchema = z.object({
  nonTaxResident: stringBoolean(),
  residentCountry: z.string().optional(),
})
  .refine(data => !(data.nonTaxResident === "false" && !data.residentCountry), {
    message: "Required.",
    path: ["residentCountry"],
  });

export type TaxResidentType = z.infer<typeof taxResidentSchema>;
