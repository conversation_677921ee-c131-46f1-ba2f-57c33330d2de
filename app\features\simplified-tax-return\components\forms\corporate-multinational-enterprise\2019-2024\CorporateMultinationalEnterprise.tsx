import { zodResolver } from "@hookform/resolvers/zod";
import { <PERSON><PERSON>, <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, Form, FormControl, FormField, FormItem, FormLabel, FormMessage, RadioGroup, RadioGroupItem, Tooltip, TooltipContent, TooltipTrigger } from "@netpro/design-system";
import { Form as RemixForm, useFetcher } from "@remix-run/react";
import { useForm } from "react-hook-form";
import { type JSX, useEffect, useMemo, useState } from "react";
import { useSubmission } from "~/lib/submission/context/use-submission";
import { Pages } from "~/lib/simplified-tax-return/utilities/form-pages";
import { useScrollToError } from "~/lib/utilities/use-scroll-to-error";
import type { CorporateMultinationalEnterpriseType } from "~/lib/simplified-tax-return/types/corporate-multinational-enterprise/2019-2024/corporate-multinational-enterprise-schema";
import { corporateMultinationalEnterpriseSchema } from "~/lib/simplified-tax-return/types/corporate-multinational-enterprise/2019-2024/corporate-multinational-enterprise-schema";

export function CorporateMultinationalEnterprise(): JSX.Element {
  const { submissionData } = useSubmission();
  const data = useMemo(() => submissionData[Pages.CORPORATE_MULTINATIONAL_ENTERPRISE] as CorporateMultinationalEnterpriseType, [submissionData]);
  const [open, setOpen] = useState(false);
  const form = useForm<CorporateMultinationalEnterpriseType>({
    resolver: zodResolver(corporateMultinationalEnterpriseSchema),
    shouldFocusError: false,
  });
  const { reset, formState } = form;
  const [canFocus, setCanFocus] = useState(true)
  useScrollToError(formState, canFocus, setCanFocus);

  function onError(): void {
    setCanFocus(true)
  }

  useEffect(() => {
    reset(data, { keepDefaultValues: true });
  }, [data, reset]);

  const fetcher = useFetcher();

  function onSubmit(data: CorporateMultinationalEnterpriseType): void {
    fetcher.submit({ data: JSON.stringify(data) }, {
      method: "post",
    });
  }

  const isPartOfMNEGroup = form.watch("isPartOfMNEGroup");

  return (
    <Form {...form}>
      <RemixForm onSubmit={form.handleSubmit(onSubmit, onError)} noValidate id="str-form">
        <div className="flex-col space-y-5">
          <FormField
            control={form.control}
            name="isPartOfMNEGroup"
            render={({ field, fieldState }) => (
              <FormItem className="max-w-2xl">
                <Tooltip delayDuration={0}>
                  <div className="flex gap-1">
                    <FormLabel className="leading-6">
                      Is the corporation part of a
                      {" "}
                      <TooltipTrigger asChild>
                        <span className="text-primary underline-offset-4 hover:underline">
                          Multinational Enterprise (MNE)
                        </span>
                      </TooltipTrigger>
                      {" "}
                      group with annual
                      {" "}
                      <Button
                        type="button"
                        variant="link"
                        onClick={() => setOpen(true)}
                        className="p-0 inline-flex items-center gap-1"
                      >
                        <span>consolidated group revenue</span>
                      </Button>
                      {" "}
                      of €750 million (or XCD equivalent) in the immediately
                      preceding fiscal
                      {" "}
                      <span className="text-nowrap">year? *</span>
                    </FormLabel>

                  </div>

                  <TooltipContent className="w-96 p-5 font-inter" side="bottom">
                    <p>
                      A multinational enterprise, abbreviated as MNE and sometimes also called multinational corporation (MNC),
                      just multinational or international corporation, is an enterprise producing goods or delivering services in more than one country.
                    </p>
                    <p>
                      A multinational enterprise has its management headquarters in one (or rarely more than one) country,
                      the home country, while also operating in other countries, the host countries.
                    </p>
                  </TooltipContent>
                </Tooltip>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    value={field.value}
                    invalid={!!fieldState.error}
                    className="flex flex-row space-x-2"
                  >
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="true" />
                      </FormControl>
                      <FormLabel className="font-normal">
                        Yes
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="false" />
                      </FormControl>
                      <FormLabel className="font-normal">
                        No
                      </FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <Dialog open={open} onOpenChange={setOpen}>
            <DialogContent className="max-w-screen-md">
              <DialogHeader className="font-inter">
                <DialogDescription>
                  <div className="flex flex-col gap-2 mb-4">
                    <p>
                      <span className="font-bold">"Gross revenue"</span>
                      {" "}
                      refers to earnings before deducting any expenses.
                    </p>
                    <p>
                      Turnover is the total sales generated by a business in a specific period;
                      sometimes referred to as gross revenue, or income.
                      Therefore, gross turnover is the total sales generated by a business before deducting any expenses in a specific period.
                      Consolidated Group or Consolidated Corporation.
                    </p>
                  </div>
                  <div className="mb-4 flex flex-col  gap-2">
                    <p>
                      <span className="font-bold">"Consolidation"</span>
                      {" "}
                      means a procedure whereby any two (2) or more corporations consolidate into a new corporation incorporated by the consolidation;
                    </p>
                    <p>
                      <span className="font-bold">"Consolidated Corporation"</span>
                      {" "}
                      means the new corporation into which two (2) or more constituent corporations are consolidated;
                    </p>
                    <p>
                      <span className="font-bold">"Constituent Corporation"</span>
                      {" "}
                      means an existing corporation that is participating in the merger or consolidation with one (1) or more other corporations;
                    </p>
                    <p>
                      <span className="font-bold">"Corporation"</span>
                      {" "}
                      or
                      <span className="font-bold"> "Domestic Corporation"</span>
                      {" "}
                      means a corporation incorporated, merged or consolidated under this Ordinance,
                      or a foreign corporation which has been redomiciled to Nevis and registered under the Ordinance.
                    </p>
                    <p>Nevis Business Corporation Ordinance, 2017 (Section 2)</p>
                  </div>

                  <div className="flex flex-col gap-2">
                    <p>
                      <span className="font-bold">"Consolidation"</span>
                      {" "}
                      means a procedure whereby any two (2) or more limited liability companies consolidate into a new limited liability company formed by the consolidation;
                    </p>
                    <p>
                      <span className="font-bold">"Consolidated Company"</span>
                      {" "}
                      means the new limited liability company into which two (2) or more constituent companies are consolidated;
                    </p>
                    <p>
                      <span className="font-bold">"Constituent Company"</span>
                      {" "}
                      means an existing limited liability company that is participating in the merger or consolidation with one (1) or more other limited liability companies;
                    </p>
                    <p>Nevis Limited Liability Company Ordinance, 2017 (Section 2)</p>
                  </div>

                </DialogDescription>
              </DialogHeader>
              <DialogFooter className="pt-4">
                <Button variant="outline" onClick={() => setOpen(false)}>Close</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {isPartOfMNEGroup === "true" && (
            <FormField
              control={form.control}
              name="requiresCbCReport"
              render={({ field, fieldState }) => (
                <FormItem className="max-w-2xl">
                  <FormLabel>
                    Is the corporation&#x2019;s data required to be reported as part of a Country-by-Country report to a tax authority of a jurisdiction outside the Federation? *
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      invalid={!!fieldState.error}
                      className="flex flex-row space-x-2"
                    >
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="true" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          Yes
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="false" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          No
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
        </div>
      </RemixForm>
    </Form>
  )
}
