import { cn } from "@netpro/design-system";
import { <PERSON> } from "@remix-run/react";
import type { JSX } from "react";

type AssistanceProps = {
  className?: string
};

export function Assistance({ className }: AssistanceProps): JSX.Element {
  return (
    <div className={cn("flex items-start gap-2", className)}>
      <div className="flex flex-col gap-0 justify-center items-start">
        <span className="text-md font-light text-gray-400">Need assistance?</span>
        <Link
          to="mailto:<EMAIL>"
          className="text-sm font-medium hover:underline"
        >
          <EMAIL>
        </Link>
      </div>
    </div>
  );
}
