import type { LoaderFunctionArgs, TypedResponse } from "@remix-run/node";
import { json } from "@remix-run/node";
import { Link, useLoaderData } from "@remix-run/react";
import type { JSX } from "react";
import { Button } from "@netpro/design-system";
import { ChevronRight } from "lucide-react";
import { Breadcrumb } from "~/components/breadcrumbs/Breadcrumb";
import { requireActiveModule } from "~/features/modules/api/get-modules";
import { Modules } from "~/lib/utilities/modules";
import { SubmissionStatusNames } from "~/lib/submission/utilities/submission-status";
import { middleware } from "~/lib/middlewares.server";
import { Pages } from "~/lib/basic-financial-report/utilities/form-pages";
import { getUnflattenedDataSet } from "~/lib/submission/utilities/submission-data-set-auto";
import type { DocumentDTO, SubmissionDTO } from "~/services/api-generated";
import { clientGetCompanyModuleSubmissions, clientGetSubmission, getApiV1CommonDocumentsByDocumentId } from "~/services/api-generated";
import { SubmissionRow } from "~/components/basic-financial-report/SubmissionRow";
import { CenteredMessage } from "~/components/errors/CenteredMessage";
import { authHeaders } from "~/lib/auth/utils/auth-headers";

const title = "Submissions for" as const;
const breadCrumbList = [
  {
    href: "/basic-financial-report/new",
    name: "Basic Financial Report",
  },
];

export type SubmissionModel = SubmissionDTO & { summaryReport?: DocumentDTO }
type LoaderResponse = {
  submissions: SubmissionModel[]
}

export const handle = {
  breadcrumb: (): JSX.Element => <Breadcrumb data={breadCrumbList} />,
  title,
};

export async function loader({ request }: LoaderFunctionArgs): Promise<TypedResponse<LoaderResponse | never>> {
  const { company } = await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany"], request);
  const { module } = await requireActiveModule({ request, key: Modules.BASIC_FINANCIAL_REPORT, companyId: company.companyId });
  const { data: submissionData, error } = await clientGetCompanyModuleSubmissions({
    headers: await authHeaders(request),
    path: {
      companyId: company.companyId,
      moduleId: module.id as string,
    },
  })

  if (error) {
    throw new Response(error.exceptionMessage as string, { status: 500 })
  }

  if (!submissionData?.data) {
    throw new Response("Submissions not found", { status: 404 })
  }

  const submissions = submissionData?.data?.filter(
    submission => submission.statusText === SubmissionStatusNames.Submitted || submission.statusText === SubmissionStatusNames.Paid,
  );
  const mappedSubmissions: SubmissionModel[] = await Promise.all(
    submissions.map(async (submission) => {
      const { data: submissionDetails } = await clientGetSubmission({
        headers: await authHeaders(request),
        path: { submissionId: submission.id! },
        query: { includeFormDocument: true },
      });

      if (!submissionDetails) {
        throw new Error("Submission not found");
      }

      const submissionData = getUnflattenedDataSet(submissionDetails);
      const documentId = submissionData[Pages.FINANCIAL_PERIOD]?.summaryReportDocumentId;
      let summaryReport: DocumentDTO | undefined;

      if (submissionDetails.documentIds?.length && documentId) {
        const { data: document } = await getApiV1CommonDocumentsByDocumentId({
          headers: await authHeaders(request),
          path: { documentId },
        });
        summaryReport = document;
      }

      return { ...submission, summaryReport } as SubmissionModel;
    }),
  );

  return json({ submissions: mappedSubmissions });
}

export default function BFRSubmissions(): JSX.Element {
  const { submissions } = useLoaderData<typeof loader>();

  return (
    <div className="flex flex-col w-full justify-between">
      <div className="px-4 py-2.5">
        {submissions && submissions.length > 0
          ? (
              submissions.map(submission => (
                <SubmissionRow
                  submission={submission}
                  key={submission.id}
                  moduleUrl="basic-financial-report"
                  continuePageName={Pages.FINANCIAL_PERIOD}
                />
              ))
            )
          : (
              <CenteredMessage title="No submissions have been completed">
                <Button asChild>
                  <Link to="./new">
                    File new submission
                    <ChevronRight />
                  </Link>
                </Button>
              </CenteredMessage>
            )}
      </div>
    </div>
  );
}
