import type { ActionFunctionArgs, LoaderFunctionArgs, TypedResponse } from "@remix-run/node";
import { json, redirect } from "@remix-run/node";
import { type Company, getCompanies } from "~/features/companies/api/get-companies";
import { commitSession, getSession } from "~/lib/auth/utils/session.server";
import { middleware } from "~/lib/middlewares.server";
import { getCompanyModuleState } from "~/features/companies/api/get-company-module-state";
import type { BasicMasterClient } from "~/features/master-clients/types/generic";

export async function loader({ request }: LoaderFunctionArgs): Promise<TypedResponse<{ companies: Company[] } | never>> {
  const { userId, accessToken } = await middleware(["auth", "mfa", "terms"], request);
  const session = await getSession(request.headers.get("Cookie"));
  const masterClient = session.get("currentMasterClient") as BasicMasterClient;
  const queuedMasterClientUpdate = session.get("queuedMasterClientUpdate") as BasicMasterClient;

  if (!masterClient && !queuedMasterClientUpdate) {
    return json({ companies: [] });
  }

  const url = new URL(request.url);
  const search = url.searchParams.get("search") || "";
  const inactive = url.searchParams.get("inactive") === "true";
  /*
   * Should return the list of user master clients
   * GET /api/v1/clients/master-clients
   */
  const data = await getCompanies({
    accessToken,
    userId,
    masterClientId: queuedMasterClientUpdate ? queuedMasterClientUpdate.masterClientId as string : masterClient.masterClientId as string,
    params: { search, includeInactive: inactive },
  });

  return json({ companies: data.companies });
}

export async function action({ request }: ActionFunctionArgs): Promise<TypedResponse> {
  const { userId, accessToken } = await middleware(["auth", "mfa", "terms"], request);
  // TODO: Might need to change this to just use the Remix native params
  const formData = new URLSearchParams(await request.text());
  const companyId = formData.get("id");
  const includeInactive = formData.get("isActive") !== "true";

  if (!companyId) {
    return json({ error: "No company selected" }, { status: 400 });
  }

  const session = await getSession(request.headers.get("Cookie"));
  const masterClient = session.get("currentMasterClient") as BasicMasterClient | undefined;
  const queuedMasterClientUpdate = session.get("queuedMasterClientUpdate") as BasicMasterClient | undefined;
  const data = await getCompanies({
    accessToken,
    userId,
    masterClientId: queuedMasterClientUpdate
      ? queuedMasterClientUpdate?.masterClientId as string
      : masterClient?.masterClientId as string,
    params: { search: "", includeInactive },
  });
  const selectedCompany = data?.companies?.find(company => company.companyId === companyId);

  if (!selectedCompany) {
    return json({ error: "Invalid company selected" }, { status: 400 });
  }

  if (queuedMasterClientUpdate) {
    session.set("currentMasterClient", queuedMasterClientUpdate)
  }

  session.unset("queuedMasterClientUpdate");
  session.set("currentCompany", selectedCompany);
  session.set("companyModules", await getCompanyModuleState({
    userId,
    accessToken,
    companyId: selectedCompany.companyId,
  }));

  return redirect("/dashboard", {
    headers: {
      "Set-Cookie": await commitSession(session),
    },
  });
}
