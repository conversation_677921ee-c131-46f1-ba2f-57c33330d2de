import type { ReactNode } from "react";
import { SwitchEntityButton } from "~/components/ui/buttons/SwitchEntityButton";
import { SwitchEntityTitle } from "~/components/ui/buttons/SwitchEntityTitle";
import MasterClientSearchDialog from "~/features/master-clients/components/MasterClientsSearchDialog";
import type { BasicMasterClient } from "~/features/master-clients/types/generic";

type SwitchMasterClientProps = {
  current?: BasicMasterClient
  total?: number | undefined
  onChange?: (isChanged?: boolean) => void
  open?: boolean
  setOpenDialog: (open: boolean) => void
};

export function SwitchMasterClient({
  current,
  onChange,
  total = 0,
  open = false,
  setOpenDialog,
}: SwitchMasterClientProps): ReactNode {
  const handleChange = (isChanged: boolean): void => {
    setOpenDialog(false);
    onChange?.(isChanged);
  }

  if (!current) {
    throw new Error("Current master client is required");
  }

  return (
    <>
      <SwitchEntityButton onClick={() => setOpenDialog(total > 1)} variant="pyramid">
        <SwitchEntityTitle prefix="Master Client">
          {current.masterClientCode}
        </SwitchEntityTitle>
      </SwitchEntityButton>

      <MasterClientSearchDialog
        open={open}
        setOpen={setOpenDialog}
        currentMasterClientId={current.masterClientId as string}
        onChange={handleChange}
      />
    </>
  );
}
