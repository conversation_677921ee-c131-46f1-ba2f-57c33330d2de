import type { JSX } from "react";
import { Alert } from "@netpro/design-system";

export function ContactAgentDetails(): JSX.Element {
  return (
    <div className="max-w-2xl drop-shadow-sm">
      <Alert title="Please contact your registered agent" variant="warning">
        <ul className="">
          <li>
            <span className="font-semibold">Phone</span>
            :
            {" "}
            <a className="hover:underline underline-offset-2" href="tel:******-469-1817">******-469-1817</a>
          </li>
          <li>
            <span className="font-semibold">E-mail</span>
            :
            {" "}
            <a className="hover:underline underline-offset-2" href="mailto:<EMAIL>"><EMAIL></a>
          </li>
        </ul>
      </Alert>
    </div>
  );
}
