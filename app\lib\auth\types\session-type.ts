import type { MethodK<PERSON>s } from "../utils/mfa";
import type { NotificationDataType } from "~/lib/types/notification-type";
import type { CompanyModuleState } from "~/features/companies/api/get-company-module-state";
import type { BasicMasterClient } from "~/features/master-clients/types/generic";

export type SessionData = {
  objectId?: string
  userEmail?: string
  accessToken?: string
  isActive?: boolean
  accessTokenExpiresOn?: string | null
  userId?: string
  userName?: string
  mfaMethod?: MethodKeys
  mfaEmailCodeExpiresAt?: string
  mfaAttempts?: number
  mfaCompleted?: boolean
  termsAndConditionsAccepted?: boolean
  notifications?: NotificationDataType
  profilePictureUrl?: string
  currentMasterClient?: BasicMasterClient
  queuedMasterClientUpdate?: BasicMasterClient
  currentCompany?: CompanyDTO
  companyModules?: CompanyModuleState
  totalMasterClients?: number
  totalMasterClientCompanies?: number
};

export type CompanyDTO = {
  companyId: string
  companyName: string
  incorporationNumber?: string
  jurisdictionId: string
  jurisdictionName: string
  isActive?: boolean
}

export type RootLoaderData = {
  sessionData: SessionData
}
