import { Badge, Label } from "@netpro/design-system";
import type { ReactNode } from "react";
import type { ListSubmissionDTO } from "~/services/api-generated";

type PendingSubmissionRowProps = {
  submission: ListSubmissionDTO
};

export function PendingSubmissionRow({ submission }: PendingSubmissionRowProps): ReactNode {
  return (
    <div
      className="flex items-center justify-start py-3 border-2 border-transparent hover:border-blue-600 group pr-5 transition-all duration-200 rounded-md"
    >
      <div className="flex items-center space-x-4">
        <Label
          htmlFor={submission.id}
        />
        <div>
          <div className="flex items-center gap-4">
            <p className="text-lg font-semibold">{submission.legalEntityName}</p>
            <Badge className="h-6" variant="info">{submission.moduleName}</Badge>
          </div>
          <div className="flex">
            <div className="flex border-gray-600 border-opacity-10 pr-2 border-r-2 mr-2 ">
              <div className="flex space-x-2">
                <p className="text-sm text-gray-600">Incorporation number</p>
                <p className="text-sm text-gray-600 font-semibold">{submission.incorporationNr}</p>
              </div>
            </div>
            <div className="flex">
              <div className="flex space-x-2">
                <p className="text-sm text-gray-600">Financial year</p>
                <p className="text-sm text-gray-600 font-semibold">{submission.financialYear}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
