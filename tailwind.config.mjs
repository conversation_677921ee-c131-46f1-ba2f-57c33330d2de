import { configuration as tailwindConfig } from "@netpro/design-system";

tailwindConfig.theme.extend.colors = {
  ...tailwindConfig.theme.extend.colors,
  blue: {
    50: "#B8DAFF",
    100: "#A3CFFF",
    200: "#7ABAFF",
    300: "#52A5FF",
    400: "#298FFF",
    500: "#007AFF",
    600: "#005FC7",
    700: "#00448F",
    800: "#002957",
    900: "#000F1F",
  },
  gray: {
    50: "#F8FAFC",
    100: "#F1F5F9",
    200: "#E2E8F0",
    300: "#CBD5E1",
    400: "#94A3B8",
    500: "#64748B",
    600: "#475569",
    700: "#334155",
    800: "#1E293B",
    900: "#0F172A",
  },
  teal: {
    50: "#EBFAF9",
    100: "#DBF6F3",
    200: "#BAEEE8",
    300: "#9AE6DE",
    400: "#79DED3",
    500: "#58D6C8",
    600: "#30C5B5",
    700: "#25988C",
    800: "#1A6B62",
    900: "#0F3E39",
  },
  orange: {
    50: "#FFFFFF",
    100: "#FFFBF9",
    200: "#FFE1D0",
    300: "#FFC8A8",
    400: "#FFAE7F",
    500: "#FF9456",
    600: "#FF701E",
    700: "#E55400",
    800: "#AD3F00",
    900: "#752B00",
  },
}

tailwindConfig.theme.extend.backgroundImage = {
  ...tailwindConfig.theme.extend.backgroundImage,
  auth: "url('/images/auth-background.jpg')",
};

tailwindConfig.theme.extend.fontFamily = {
  ...tailwindConfig.theme.extend.fontFamily,
  "inter": ["Inter", "sans-serif"],
  "arial": ["Arial"],
  "plaak-regular": ["plaakRegular"],
  "plaak-bold": ["plaakBold"],
  "plaak-heavy": ["plaakHeavy"],
  "akkurat-light": ["AkkuratLLSub-Light"],
};

export default {
  ...tailwindConfig,
  content: ["./app/**/*.{ts,tsx}", "./node_modules/@netpro/design-system/**/*.js"],
};
