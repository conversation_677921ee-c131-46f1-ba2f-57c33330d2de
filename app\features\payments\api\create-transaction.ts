import { z } from "zod";

export const createTransactionSchema = z.object({
  firstName: z.string().min(1, "Required"),
  lastName: z.string().min(1, "Required"),
  email: z.string().email("Invalid email").min(1, "Required"),
  description: z.string().min(1, "Required"),
  orderId: z.string().min(1, "Required"),
  paymentRedirectUrl: z.string().url("Invalid URL").min(1, "Required"),
  cancelUrl: z.string().url("Invalid URL").min(1, "Required"),
  companyName: z.string().min(1, "Required"),
  phoneNumber: z.string().min(1, "Required"),
  merchantEmail: z.string().email("Invalid email").min(1, "Required"),
});

export type CreateTransactionType = z.infer<typeof createTransactionSchema>;

export type PaymentProcessorResponse = {
  transactionId: string
  providerTransactionId: string
  callBackUrl: string
  result: number
  resultText: string
  resultNumber: number
  htmlContent: string
  consumer: {
    applicationId: string
    id: string
    name: string
  }
  provider: {
    id: string
    name: string
  }
}

export type CreatedTransaction = {
  paymentProcessorResponse: PaymentProcessorResponse
}
