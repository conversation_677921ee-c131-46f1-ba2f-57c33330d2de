import { useLoaderData } from "@remix-run/react";
import type { ReactNode } from "react";
import { useProfitLossCalculation } from "~/lib/basic-financial-report/hooks/report/profit-loss/use-calculation";
import { transformNumberToCurrency } from "~/lib/basic-financial-report/utilities/currencies";
import { Pages } from "~/lib/basic-financial-report/utilities/form-pages";
import { formatDate } from "~/lib/utilities/format";
import type { BasicFinancialReportSummaryLoader } from "~/routes/_pdf.basic-financial-report.$id.summary";

export function ProfitLoss(): ReactNode {
  const { submissionData } = useLoaderData<BasicFinancialReportSummaryLoader>()
  const startDate = formatDate(submissionData[Pages.FINANCIAL_PERIOD].startFiscalYear)
  const endDate = formatDate(submissionData[Pages.FINANCIAL_PERIOD].endFiscalYear)
  const {
    investmentIncome,
    netRealisedGainOnInvestments,
    netUnrealisedGainOnInvestments,
    otherIncome,
    totalIncome,
    companyAdministrationFees,
    portfolioManagementFeesAndRelatedServices,
    interestExpense,
    otherExpenses,
    totalExpense,
    operatingProfitLossBeforeTax,
    tax,
    profitAfterTax,
    dividendsPaid,
    netIncomeLoss,
  } = useProfitLossCalculation(submissionData)

  return (
    <div className="text-xs leading-tight">
      {/* Header */}
      <div className="text-center mb-4 font-bold">
        <h2 className="text-base mb-1 uppercase">
          {`STATEMENT OF INCOME AND EXPENSES FOR THE PERIOD FROM ${startDate}`}
        </h2>
        <h3 className="text-base mb-1 uppercase">{`TO ${endDate}`}</h3>
      </div>

      {/* Financial Statement Table */}
      <table className="w-full border-collapse">
        <tbody>
          <tr>
            <td className="font-bold pb-2">INCOME</td>
            <td className="text-right font-bold pb-2">USD</td>
          </tr>
          <tr>
            <td>INVESTMENT INCOME</td>
            <td className="text-right">{transformNumberToCurrency(investmentIncome)}</td>
          </tr>
          <tr>
            <td>NET REALISED GAIN ON INVESTMENTS</td>
            <td className="text-right">{transformNumberToCurrency(netRealisedGainOnInvestments)}</td>
          </tr>
          <tr>
            <td>NET UNREALISED GAIN ON INVESTMENTS</td>
            <td className="text-right">{transformNumberToCurrency(netUnrealisedGainOnInvestments)}</td>
          </tr>
          <tr>
            <td>OTHER INCOME</td>
            <td className="text-right border-b border-black">{transformNumberToCurrency(otherIncome)}</td>
          </tr>
          <tr>
            <td></td>
            <td className="text-right">{transformNumberToCurrency(totalIncome)}</td>
          </tr>

          <tr>
            <td className="font-bold pt-3 pb-2">EXPENSES</td>
            <td></td>
          </tr>
          <tr>
            <td>NET REALISED LOSS ON INVESTMENTS</td>
            <td className="text-right"></td>
          </tr>
          <tr>
            <td>NET UNREALISED LOSS ON INVESTMENTS</td>
            <td className="text-right"></td>
          </tr>
          <tr>
            <td>COMPANY ADMINISTRATION FEES</td>
            <td className="text-right">{transformNumberToCurrency(companyAdministrationFees)}</td>
          </tr>
          <tr>
            <td>PORTFOLIO MANAGEMENT FEES AND RELATED SERVICES</td>
            <td className="text-right">{transformNumberToCurrency(portfolioManagementFeesAndRelatedServices)}</td>
          </tr>
          <tr>
            <td>WITHHOLDING TAXES</td>
            <td className="text-right"></td>
          </tr>
          <tr>
            <td>INTEREST EXPENSE</td>
            <td className="text-right">{transformNumberToCurrency(interestExpense)}</td>
          </tr>
          <tr>
            <td>OTHER EXPENSES</td>
            <td className="text-right border-b border-black">{transformNumberToCurrency(otherExpenses)}</td>
          </tr>
          <tr>
            <td></td>
            <td className="text-right">{transformNumberToCurrency(totalExpense)}</td>
          </tr>

          <tr>
            <td className="font-bold pt-3 pb-2">OPERATING PROFIT/(LOSS) BEFORE TAX</td>
            <td className="text-right pb-2 font-bold">{`(${transformNumberToCurrency(operatingProfitLossBeforeTax)})`}</td>
          </tr>
          <tr>
            <td className="pb-3">TAX</td>
            <td className="text-right border-b border-black pb-3">{transformNumberToCurrency(tax)}</td>
          </tr>

          <tr>
            <td className="font-bold pb-5">PROFIT AFTER TAX</td>
            <td className="text-right pb-5">{`(${transformNumberToCurrency(profitAfterTax)})`}</td>
          </tr>

          <tr>
            <td className="pb-5">DIVIDENDS PAID</td>
            <td className="text-right border-b border-black pb-5">{`(${transformNumberToCurrency(dividendsPaid)})`}</td>
          </tr>

          <tr>
            <td className="italic font-bold">NET INCOME / (LOSS)</td>
            <td className="text-right border-b border-black font-bold">{`(${transformNumberToCurrency(netIncomeLoss)})`}</td>
          </tr>
        </tbody>
      </table>
    </div>
  )
}
