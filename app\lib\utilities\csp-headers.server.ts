import { Buffer } from "node:buffer";

export function cspHeaders(nonce: string): string {
  return `
            default-src 'self';
            style-src 'unsafe-inline' 'self';
            script-src 'self' 'nonce-${nonce}' 'strict-dynamic';
            img-src 'self' blob: data:;
            connect-src 'self' https://cxpay.transactiongateway.com;
            font-src 'self';
            frame-ancestors 'none';
          `.replace(/\s{2,}/g, " ").trim();
}

export function generateNonce(): string {
  return Buffer.from(crypto.randomUUID()).toString("base64");
}
