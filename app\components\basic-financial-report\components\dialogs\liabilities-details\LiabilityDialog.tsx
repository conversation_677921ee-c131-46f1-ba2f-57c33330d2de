import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON>alog<PERSON><PERSON><PERSON>,
  DialogHeader,
  DialogTitle,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  ScrollArea,
  ScrollBar,
} from "@netpro/design-system";
import type { ReactNode } from "react";
import type { UseFormReturn } from "react-hook-form";
import { Form as RemixForm } from "@remix-run/react"
import { CurrencyInput } from "~/components/ui/inputs/CurrencyInput";
import { Currency } from "~/lib/basic-financial-report/utilities/currencies";
import type { LiabilitySchemaType } from "~/lib/basic-financial-report/types/liabilities-details-schema";

type Props = {
  open: boolean
  setOpen: (open: boolean) => void
  onSubmit: (data: LiabilitySchemaType) => void
  form: UseFormReturn<LiabilitySchemaType>
};

const FORM_ID = "liability-dialog-form"

export function LiabilityDialog({
  open,
  setO<PERSON>,
  onSubmit,
  form,
}: <PERSON>ps): ReactNode {
  return (
    <Dialog open={open} onOpenChange={setOpen} modal>
      <DialogContent
        className="max-w-screen-sm"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <ScrollArea className="pr-3">
          <Form {...form}>
            <RemixForm onSubmit={form.handleSubmit(onSubmit)} className="p-2" noValidate id={FORM_ID}>
              <DialogHeader>
                <DialogTitle>Liability</DialogTitle>
              </DialogHeader>
              <div className="flex-col space-y-2 pt-4">
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field, fieldState }) => (
                    <FormItem className="md:w-1/2 sm:w-full">
                      <FormLabel>Description*</FormLabel>
                      <FormControl>
                        <Input
                          invalid={!!fieldState.error}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="current"
                  render={({ field, fieldState }) => (
                    <FormItem className="md:w-1/2 sm:w-full">
                      <FormLabel>Current*</FormLabel>
                      <FormControl>
                        <CurrencyInput
                          currencyName={Currency.USD}
                          invalid={!!fieldState.error}
                          {...field}
                          type="number"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="nonCurrent"
                  render={({ field, fieldState }) => (
                    <FormItem className="md:w-1/2 sm:w-full">
                      <FormLabel>Non-Current*</FormLabel>
                      <FormControl>
                        <CurrencyInput
                          currencyName={Currency.USD}
                          invalid={!!fieldState.error}
                          {...field}
                          type="number"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <DialogFooter className="pt-4">
                <div className="flex w-full justify-end">
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline" onClick={() => setOpen(false)} type="button">Cancel</Button>
                    <Button size="sm" variant="default" type="submit" form={FORM_ID}>Save</Button>
                  </div>
                </div>
              </DialogFooter>
            </RemixForm>
          </Form>
          <ScrollBar />
        </ScrollArea>
      </DialogContent>
    </Dialog>
  )
}
