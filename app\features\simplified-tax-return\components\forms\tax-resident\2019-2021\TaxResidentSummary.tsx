import { Table, TableBody, TableCell, TableRow } from "@netpro/design-system";
import type { JSX } from "react";
import type { TaxResidentType } from "~/lib/simplified-tax-return/types/tax-resident/2019-2021/tax-resident-schema";
import { Pages } from "~/lib/simplified-tax-return/utilities/form-pages";
import { useSubmission } from "~/lib/submission/context/use-submission";
import { getCountryName } from "~/lib/utilities/countries";
import { formatYesNoBoolean } from "~/lib/utilities/format";

export function TaxResidentSummary(): JSX.Element {
  const { submissionData } = useSubmission();
  const taxResident = submissionData[Pages.TAX_RESIDENT] as TaxResidentType;
  const countryName = taxResident?.residentCountry === ""
    ? "No country"
    : (taxResident?.residentCountry ? getCountryName(taxResident.residentCountry) : "N/A")

  return (
    <section id="report-details-section">
      <h2 className="text-lg font-semibold">Report Details</h2>

      <Table className="border border-blue-600 pointer-events-none mb-2">
        <TableBody>
          <TableRow className="border border-blue-600">
            <TableCell className="w-2/3 py-1">
              <span>Were you incorporated before January 1, 2019?</span>
            </TableCell>
            <TableCell className="w-1/3 text-center py-1">
              <span className="font-semibold ">{formatYesNoBoolean(taxResident?.incorporatedBefore2019)}</span>
            </TableCell>
          </TableRow>
          {taxResident?.incorporatedBefore2019 === "false" && (
            <>
              <TableRow className="border border-blue-600">
                <TableCell className="w-2/3 py-1">
                  <span>Are you either a tax resident or a non-resident with a permanent establishment in Saint Kitts and Nevis?</span>
                </TableCell>
                <TableCell className="w-1/3 text-center py-1">
                  <span className="font-semibold py-1">{formatYesNoBoolean(taxResident?.nonTaxResident)}</span>
                </TableCell>
              </TableRow>
              <TableRow className="border border-blue-600 py-1">
                <TableCell className="w-2/3 py-1">
                  <span>Where are you resident for tax purposes?</span>
                </TableCell>
                <TableCell className="w-1/3 text-center py-1">
                  <span className="font-semibold">{countryName}</span>
                </TableCell>
              </TableRow>
            </>
          )}
        </TableBody>
      </Table>
    </section>
  );
}
