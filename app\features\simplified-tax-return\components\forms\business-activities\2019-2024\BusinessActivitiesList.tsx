import { <PERSON>roll<PERSON><PERSON>, ScrollBar } from "@netpro/design-system";
import type { JSX } from "react";
import type { BusinessActivityType } from "~/lib/simplified-tax-return/types/business-activity/2019-2024/business-activity-schema";
import {
  BusinessActivityRow,
} from "~/features/simplified-tax-return/components/forms/business-activities/2019-2024/BusinessActivityRow";

type BusinessActivitiesTableProps = {
  businessActivities: (BusinessActivityType & { formArrayId: string })[]
  onSelect: (businessActivity: BusinessActivityType, index: number) => void
}

export function BusinessActivitiesList({ businessActivities, onSelect }: BusinessActivitiesTableProps): JSX.Element {
  return (
    <div className="border-gray-200 border mt-4 rounded-md shadow-sm">
      <ScrollArea>
        {!businessActivities.length && (
          <div className="p-4 text-center">
            <p>No Business Activities added</p>
          </div>
        )}
        {businessActivities && businessActivities.map((businessActivity, index) => (
          <BusinessActivityRow businessActivity={businessActivity} onClick={onSelect} index={index} key={businessActivity.formArrayId} />
        ))}
        <ScrollBar orientation="horizontal" />
      </ScrollArea>
    </div>
  )
}
