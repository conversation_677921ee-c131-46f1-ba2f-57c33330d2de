import { format, parseISO } from "date-fns";

export function formatDate(date: Date | string): string {
  if (typeof date === "string") {
    if (!date.endsWith("Z")) {
      date = `${date}Z`;
    }

    date = parseISO(date);
  }

  return format(date, "dd-MMM-yyyy");
}

export function formatYesNoBoolean(value: "true" | "false" | boolean | undefined): string | undefined {
  if (typeof value === "boolean") {
    return value ? "Yes " : "No";
  }

  if (typeof value === "string") {
    return value === "true" ? "Yes" : "No";
  }

  return undefined;
}
