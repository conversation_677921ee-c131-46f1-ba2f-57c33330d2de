import { type ActionFunctionArgs, type TypedResponse, json } from "@remix-run/node";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { middleware } from "~/lib/middlewares.server";
import { clientDeleteSubmission } from "~/services/api-generated";

export async function action({ request }: ActionFunctionArgs): Promise<TypedResponse<{ success: boolean, data?: any, error?: string }>> {
  await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany"], request);
  const formData = await request.formData();
  const result = await clientDeleteSubmission({
    headers: await authHeaders(request),
    path: {
      submissionId: formData.get("submissionId") as string,
    },
  })

  if ("error" in result && result.error) {
    return json({
      success: false,
      errors: {
        SubmissionDelete: result.error.exceptionMessage as string,
      },
    })
  }

  return json({
    success: true,
  })
}
