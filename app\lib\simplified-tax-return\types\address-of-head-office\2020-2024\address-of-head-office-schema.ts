import { z } from "zod";
import { nonEmptyString, stringBoolean } from "~/lib/utilities/zod-validators";

export const addressOfHeadOfficeSchema = z.object({
  address1: nonEmptyString("Address #1"),
  address2: z.string().optional(),
  city: nonEmptyString("City"),
  zipCode: nonEmptyString("Zip code"),
  country: nonEmptyString("Country"),
  companyClassification: z.enum((["IBC", "LLC"])),
  isAddressInNevisDifferent: stringBoolean(),
  nevisAddress1: z.string().optional(),
  nevisAddress2: z.string().optional(),
  nevisCity: z.string().optional(),
  nevisZipCode: z.string().optional(),
  nevisCountry: z.string().optional(),
})
  .refine(data => !(data.isAddressInNevisDifferent === "true" && !data.nevisAddress1?.length), {
    message: "Address #1 is required",
    path: ["nevisAddress1"],
  })
  .refine(data => !(data.isAddressInNevisDifferent === "true" && !data.nevisCity?.length), {
    message: "City is required",
    path: ["nevisCity"],
  })
  .refine(data => !(data.isAddressInNevisDifferent === "true" && !data.nevisZipCode?.length), {
    message: "Zip code is required",
    path: ["nevisZipCode"],
  })
  .refine(data => !(data.isAddressInNevisDifferent === "true" && !data.nevisCountry?.length), {
    message: "Country is required",
    path: ["nevisCountry"],
  });

export type AddressOfHeadOfficeType = z.infer<typeof addressOfHeadOfficeSchema>;
