import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Sep<PERSON>tor,
  <PERSON><PERSON>,
  <PERSON>et<PERSON>ontent,
} from "@netpro/design-system";
import { useLocation, useOutletContext } from "@remix-run/react";
import { Menu } from "lucide-react";
import {
  type JSX,
  type ReactNode,
  memo,
  useState,
} from "react";
import { Assistance } from "../assistance/Assistance";
import MenuItemComponent from "./MenuItem";
import ParentMenu from "./ParentMenu";
import { Profile } from "~/components/profile/Profile";
import { Logo } from "~/components/ui/branding/Logo";
import { SwitchEntities } from "~/components/ui/buttons/SwitchEntities";
import menu from "~/lib/utilities/sidebar-menu";
import type { CompanyModuleState } from "~/features/companies/api/get-company-module-state";

type SidebarProps = {
  children: ReactNode
};

const MenuList = memo(({ isActive }: { isActive: (item: { href: string }) => boolean }) => {
  const { companyModules }: { companyModules: CompanyModuleState } = useOutletContext();

  return (
    <li>
      <ul>
        {menu(companyModules).map(
          item => item.show
          && (item.children && item.children?.length > 0
            ? (
                <ParentMenu item={item} key={item.href} active={isActive(item)} />
              )
            : (
                <MenuItemComponent
                  item={item}
                  key={item.href}
                  active={isActive(item)}
                />
              )),
        )}
      </ul>
    </li>
  );
});

MenuList.displayName = "MenuList";

export function Sidebar({ children }: SidebarProps): JSX.Element | null {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const location = useLocation();
  const isActive = (item: { href: string }): boolean => {
    if (item.href !== "/") {
      return location.pathname.startsWith(item.href);
    }

    return location.pathname === item.href;
  };

  return (
    <>
      <div className="sticky top-0 z-40 flex items-center gap-x-6 bg-white p-4 shadow-sm sm:px-6 lg:hidden ">
        <Button variant="ghost" className="-m-2.5 p-2.5 text-gray-700 lg:hidden" onClick={() => setSidebarOpen(true)}>
          <span className="sr-only">Open sidebar</span>
          <Menu className="size-6" aria-hidden="true" />
        </Button>
        <div className="flex-1 text-sm font-semibold leading-6 text-white">Dashboard</div>
        <Profile
          alignment="right"
        />
      </div>

      <div>
        <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>
          <SheetContent side="left" className="p-0">
            <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-white px-4 pt-4 pb-2">
              <div className="flex h-9 w-auto shrink-0 px-2.5 items-center gap-2.5">
                <Logo className="py-2 h-9 " />
              </div>
              <nav className="flex flex-1 flex-col">
                <ul className="flex flex-1 flex-col justify-between">
                  <MenuList isActive={isActive} />
                </ul>
                <div className="absolute bottom-2.5 left-2.5 right-2.5">
                  <div className="flex flex-col w-full gap-1">
                    <Assistance className="flex flex-col justify-start items-start" />
                    <Separator className="my-2.5" />
                    <SwitchEntities />
                  </div>
                </div>
              </nav>
            </div>
          </SheetContent>
        </Sheet>
      </div>

      <div className="hidden lg:fixed lg:inset-y-0 lg:left-0 lg:z-0 lg:flex lg:w-[270px] lg:max-w-[270px] lg:flex-col">
        <ScrollArea className="lg:w-[270px] lg:max-w-[270px]">
          <div className="flex min-h-screen flex-col gap-y-5 border-r border-gray-200 bg-white p-1.5 lg:w-[270px] lg:max-w-[270px]">
            <div className="flex min-h-12 w-full shrink-0 items-center py-0 px-2.5 gap-2.5">
              <Logo className="h-9 py-2" />
            </div>
            <nav className="flex flex-1 flex-col">
              <ul className="flex flex-1 flex-col gap-1">
                <MenuList isActive={isActive} />
                <li className="mt-auto">
                  <div className="flex flex-col w-full p-2.5 gap-1">
                    <Assistance className="flex flex-col justify-start items-start" />
                    <Separator className="my-2.5" />
                    <Profile alignment="left" className="max-w-56" />
                    <Separator className="my-2.5" />
                    <SwitchEntities />
                  </div>
                </li>
              </ul>
            </nav>
            <ScrollBar />
          </div>
        </ScrollArea>
      </div>

      {children}
    </>
  );
}
