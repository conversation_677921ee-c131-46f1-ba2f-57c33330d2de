import {
  Bread<PERSON><PERSON>bItem,
  B<PERSON><PERSON>rumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
  Breadcrumb as NPBreadCrumb,
} from "@netpro/design-system";
import type { JSX } from "react";
import { Fragment } from "react";

export type BreadCrumbProps = {
  data: {
    href: string
    name: string
  }[]
}

export function Breadcrumb({ data }: BreadCrumbProps): JSX.Element {
  return (
    <NPBreadCrumb>
      <BreadcrumbList>
        {data.map((item, index) => {
          const single = index === data.length - 1;

          if (single) {
            return (
              <BreadcrumbItem key={item.href}>
                <BreadcrumbPage className="text-sm font-semibold font-inter text-gray-500">
                  {item.name}
                </BreadcrumbPage>
              </BreadcrumbItem>
            );
          }

          return (
            <Fragment key={item.href}>
              <BreadcrumbItem>
                <BreadcrumbLink
                  className="text-sm font-semibold font-inter text-gray-500"
                  to={item.href}
                >
                  {item.name}
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
            </Fragment>
          )
        })}
      </BreadcrumbList>
    </NPBreadCrumb>
  );
}
