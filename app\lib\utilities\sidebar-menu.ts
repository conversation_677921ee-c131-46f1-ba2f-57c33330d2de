import {
  Home,
  Landmark,
  UserRoundCheck,
  Wallet,
} from "lucide-react";
import type { MenuItem } from "~/components/layout/sidebar/MenuItem";
import type { CompanyModuleState } from "~/features/companies/api/get-company-module-state";
import { DEFAULT_MODULE_STATE } from "~/features/companies/api/get-company-module-state";
import { strEntityTypeGuard } from "~/routes/_main._card.simplified-tax-return";

// Define the structure of the menu items
export default function menu(companyModules: CompanyModuleState): MenuItem[] {
  const moduleState = companyModules ?? DEFAULT_MODULE_STATE;

  return [
    {
      icon: Home,
      label: "Dashboard",
      href: "/dashboard",
      show: true,
    },
    {
      icon: Landmark,
      label: "Simplified Tax Returns",
      href: "/simplified-tax-return",
      show: moduleState.simplifiedTaxReturn.enabled,
      entityTypeGuard: strEntityTypeGuard,
      children: [
        {
          label: "Create a submission",
          href: "/simplified-tax-return/new",
          show: true,
        },
        {
          label: "Draft Submissions",
          href: "/simplified-tax-return/drafts",
          show: true,
        },
        {
          label: "Completed Submissions",
          href: "/simplified-tax-return/submissions",
          show: true,
        },
      ],
    },
    {
      icon: Landmark,
      label: "Basic Financial Report",
      href: "/basic-financial-report",
      show: moduleState.basicFinancialReport.enabled,
      children: [
        {
          label: "New submission",
          href: "/basic-financial-report/new",
          show: true,
        },
        {
          label: "Submissions",
          href: "/basic-financial-report/submissions",
          show: true,
        },
        {
          label: "Drafts",
          href: "/basic-financial-report/drafts",
          show: true,
        },
      ],
    },
    {
      icon: UserRoundCheck,
      label: "Ownership & Officers",
      href: "/bo-directors",
      show: moduleState.boDirectors.enabled,
      children: [
        {
          label: "Beneficial Owners",
          href: "/bo-directors/beneficial-owner",
          show: true,
        },
        {
          label: "Statutory Officers",
          href: "/bo-directors/director",
          show: true,
        },
      ],
    },
    {
      icon: Wallet,
      label: "Payments",
      href: "/payments",
      show: true,
      children: [
        {
          label: "Pending Payments",
          href: "/payments/pending",
          show: true,
        },
        {
          label: "Invoices",
          href: "/payments/invoices",
          show: true,
        },
      ],
    },
  ] as MenuItem[];
}
