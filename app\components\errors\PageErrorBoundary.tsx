import { isRouteErrorResponse, useRouteError } from "@remix-run/react";
import { Search, ShieldX } from "lucide-react";
import type { JSX, ReactNode } from "react";
import { CenteredMessage } from "./CenteredMessage";

/**
 * PageErrorBoundary is a universal error boundary component that will
 * catch any error that occurs in the children components.
 */
export function PageErrorBoundary({ children }: { children?: ReactNode }): JSX.Element {
  const error = useRouteError();
  let title = "Something went wrong. Please try again later.";
  const subtitle = "Use the menu on the left to navigate to another page.";
  let Icon = ShieldX;

  if (isRouteErrorResponse(error)) {
    switch (error.status) {
      case 404:
        Icon = Search;
        title = "Page not found.";
        break
      case 412:
        title = error.data;
    }
  }

  return (
    <CenteredMessage IconComponent={Icon} title={title} subtitle={subtitle}>
      {children || " "}
    </CenteredMessage>
  );
}
