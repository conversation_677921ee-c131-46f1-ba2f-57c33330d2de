import { client } from "~/lib/api-client";
import type { FormYear } from "~/lib/simplified-tax-return/hooks/use-form-steps";
import type { ClientRequestHeaders } from "~/lib/types/client-request-headers";
import type { SubmissionStatus } from "~/services/api-generated";

export type FormRevision = {
  id: string
  createdAt: string
  revision: number
  status: number
  statusText: string
  formBuilder: {
    form: {
      id: string
      dataSet: Record<string, string>
      createdBy: string
      createdAt: string
      name: string
      description: string
      version: string
    }
  }
}

export type FormDocument = {
  id: string
  name: string
  createdAt: string
  status: number
  statusText: string
  revisions: FormRevision[]
}

export type Submission = {
  id: string
  name: string
  financialYear: FormYear
  createdAt: string
  updatedAt?: string
  submittedAt?: string
  invoiceId?: string
  isPaid?: boolean
  status: SubmissionStatus
  statusText: string
  formDocument: FormDocument
}

type GetSubmissionParams = {
  id: string
  query?: {
    includeFormDocument?: boolean
  }
}

export function getSubmission({ id, query, accessToken, userId }: GetSubmissionParams & ClientRequestHeaders): Promise<Submission> {
  return client.get<Submission>(
    `/client/submissions/${id}`,
    accessToken,
    userId,
    { params: query },
  );
}
