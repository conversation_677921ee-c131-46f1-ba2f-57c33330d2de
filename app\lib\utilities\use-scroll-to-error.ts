import { useEffect } from "react";
import type { FieldValues, FormState } from "react-hook-form";

export function useScrollToError<T extends FieldValues>(
  formState: FormState<T>,
  canFocus: boolean,
  setCanFocus: (value: boolean) => void,
): void {
  useEffect(() => {
    if (canFocus) {
      const errorElements = Array.from(document.querySelectorAll("[aria-invalid=\"true\"]"));

      // Sort elements based on their position on the page.
      errorElements.sort((a, b) => a.getBoundingClientRect().top - b.getBoundingClientRect().top);

      if (errorElements.length > 0) {
        const errorElement = errorElements[0];
        errorElement.scrollIntoView({ behavior: "smooth", block: "center" }); // scrollIntoView options are not supported in Safari
        (errorElement as HTMLElement).focus({ preventScroll: true });
        setCanFocus(false); // Prevent the form from jumping to the next input that has an error.
      }
    }
  }, [formState, canFocus, setCanFocus]);
}
