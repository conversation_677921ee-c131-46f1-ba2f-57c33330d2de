import type { ActionFunctionArgs, TypedResponse } from "@remix-run/node";
import { json, redirect } from "@remix-run/node";
import { getCurrentStep, getNextStep } from "../hooks/use-form-steps";
import { Pages } from "./form-pages";
import { getSubmission } from "~/features/submissions/api/get-submission";
import { updateSubmissionDataset } from "~/features/submissions/api/update-submission-dataset";
import { commitSession, getSession } from "~/lib/auth/utils/session.server";
import { finalizeSubmission } from "~/features/submissions/api/finalize-submission";
import { getFlattenedSubmission, getUnflattenedDataSet } from "~/lib/submission/utilities/submission-data-set";
import { middleware } from "~/lib/middlewares.server";
import { SubmissionStatusNames } from "~/lib/submission/utilities/submission-status";

export async function getFormAction({ request, params }: ActionFunctionArgs, page: string): Promise<TypedResponse<null> | undefined> {
  const { userId, accessToken } = await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany", "requireStrModule"], request);
  const session = await getSession(request.headers.get("Cookie"));
  const { id } = params;

  if (!id) {
    throw new Error("Submission ID is required");
  }

  const formData = await request.formData();
  const data = JSON.parse(formData.get("data") as string);
  const submission = await getSubmission({
    id,
    accessToken,
    userId,
    query: { includeFormDocument: true },
  });
  const currentStep = getCurrentStep(submission.financialYear, page);
  if (!currentStep) {
    throw new Error("Current step is not available");
  }

  const parsedData = currentStep.validationSchema.parse(data);
  const unflattenedData = getUnflattenedDataSet(submission);
  // Update the submission dataset with the new data but keep the other pages intact
  const unflattenedNewSubmission = {
    ...unflattenedData,
    [page]: parsedData,
  };
  const newSubmissionData = getFlattenedSubmission(unflattenedNewSubmission);

  try {
    await updateSubmissionDataset({
      id,
      submission: {
        dataSet: newSubmissionData,
      },
      accessToken,
      userId,
    });
    if (page === Pages.FINALIZE) {
      if (submission.statusText !== SubmissionStatusNames.Draft && submission.statusText !== SubmissionStatusNames.Revision) {
        session.flash("notification", { title: "Error!", message: "Submission was already submitted", variant: "error" });

        return redirect("/simplified-tax-return/new", { headers: { "Set-Cookie": await commitSession(session) } });
      }

      // Finalize the submission
      await finalizeSubmission({ id, accessToken, userId });

      return redirect(`/simplified-tax-return/${params.id}/confirmation`);
    }

    const nextPage = getNextStep(unflattenedNewSubmission, submission.financialYear, page);
    if (nextPage) {
      return redirect(`/simplified-tax-return/${params.id}/${nextPage}`);
    }
  } catch (error) {
    if (error instanceof Response) {
      const errorMessage = await error.text();

      if (errorMessage) {
        session.flash("notification", { title: "An Error has occurred", message: errorMessage, variant: "error" });

        return json(null, { status: error.status, headers: { "Set-Cookie": await commitSession(session) } });
      }
    } else {
      throw error;
    }
  }
}
