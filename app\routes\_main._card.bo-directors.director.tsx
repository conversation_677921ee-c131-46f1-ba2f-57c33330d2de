import { <PERSON><PERSON>, Separator } from "@netpro/design-system"
import type { LoaderFunctionArgs } from "@remix-run/node"
import { json } from "@remix-run/node"
import { useLoaderData } from "@remix-run/react"
import type { ReactNode } from "react"
import { Breadcrumb } from "~/components/breadcrumbs/Breadcrumb"
import { CenteredMessage } from "~/components/errors/CenteredMessage"
import DirectorTable from "~/features/bo-directors/components/DirectorTable"
import { RequestAssistanceButton } from "~/features/bo-directors/components/RequestAssistanceModal"
import { authHeaders } from "~/lib/auth/utils/auth-headers"
import { hasEmptyFields } from "~/lib/bo-directors/utilities/bo-dir-empty-fields"
import { getRequiredDirFields } from "~/lib/bo-directors/utilities/bo-directors-get-required-columns"
import { middleware } from "~/lib/middlewares.server";
import { clientGetCompanyDirectors } from "~/services/api-generated"

const title = "Statutory Officers for" as const
const breadCrumbList = [
  {
    href: "/",
    name: "Ownership & Officers",
  },
]

export const handle = {
  breadcrumb: (): ReactNode => <Breadcrumb data={breadCrumbList} />,
  title,
}

export async function loader({ request }: LoaderFunctionArgs) {
  const { company } = await middleware([
    "auth",
    "mfa",
    "terms",
    "requireMcc",
    "requireCompany",
    "requireBoDirModule",
  ], request);
  const { data: directorsData, error } = await clientGetCompanyDirectors({
    headers: await authHeaders(request),
    path: { companyId: company.companyId },
  });

  if (error) {
    throw json({ error }, { status: 500 });
  }

  let { directors } = directorsData;
  directors = directors || [];
  const individualDirectors = directors.filter(director => director.isIndividual);
  const corporateDirectors = directors.filter(director => !director.isIndividual);
  const individualRequiredFields = getRequiredDirFields("INDIVIDUAL") as string[];
  const corporateRequiredFields = getRequiredDirFields("CORPORATE") as string[];
  const hasMissingFields
    = individualDirectors.some(director => hasEmptyFields(director, individualRequiredFields))
    || corporateDirectors.some(director => hasEmptyFields(director, corporateRequiredFields));

  return json({
    company,
    individualDirectors,
    corporateDirectors,
    hasMissingFields,
  })
}

export default function Directors(): ReactNode {
  const { company, individualDirectors, corporateDirectors, hasMissingFields }
    = useLoaderData<typeof loader>()

  return (
    <div className="flex flex-col w-full justify-between">
      {
        company.isActive
          ? (
              <div className="px-4 py-2.5">
                {hasMissingFields && (
                  <div className="mb-4 mt-2">
                    <Alert variant="error" title="Mandatory data is missing">
                      Mandatory data is missing for the below person(s). Please click
                      "Request update" and provide the missing information.
                    </Alert>
                  </div>
                )}

                {(!individualDirectors.length && !corporateDirectors.length)
                  ? (
                      <CenteredMessage title="No Statutory Officers found">
                        <RequestAssistanceButton
                          companyId={company.companyId}
                          assistanceRequestType="NoDirector"
                          assistanceRequestComments="Assistance needed for Directors"
                        />
                      </CenteredMessage>
                    )
                  : (
                      <div className="flex flex-col gap-5">
                        {individualDirectors.length > 0 && (
                          <DirectorTable
                            title="Individual Officers"
                            items={individualDirectors}
                            type="INDIVIDUAL_DIRECTOR"
                            requiredFields={getRequiredDirFields("INDIVIDUAL") as string[]}
                            visibleColumns={["name", "appointmentDate", "officerTypeName"]}
                          />
                        )}
                        {corporateDirectors.length > 0 && (
                          <>
                            {individualDirectors.length > 0 && (
                              <div className="mt-4">
                                <Separator />
                              </div>
                            )}
                            <DirectorTable
                              title="Corporate Officers"
                              items={corporateDirectors}
                              type="CORPORATE_DIRECTOR"
                              requiredFields={getRequiredDirFields("CORPORATE") as string[]}
                              visibleColumns={["name", "appointmentDate", "officerTypeName"]}
                            />
                          </>
                        )}
                      </div>
                    )}
              </div>
            )
          : (
              <div className="px-4 py-2.5">

                <div className="mb-4 mt-2">
                  <Alert variant="error" title="Company is closed">
                    Information for Closed Companies is not available within this system.
                    If you believe the Company's status is incorrect, please contact your usual
                    Trident representative or click the button below.
                  </Alert>
                </div>
                <div className="flex flex-col items-center justify-center">
                  <RequestAssistanceButton
                    companyId={company.companyId}
                    assistanceRequestType="NoDirector"
                    assistanceRequestComments="Assistance needed for Directors"
                  />
                </div>
              </div>
            )
      }
    </div>
  )
}
