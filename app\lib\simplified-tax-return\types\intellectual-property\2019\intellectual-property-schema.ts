import { z } from "zod";
import { nonNullDate, stringBoolean } from "~/lib/utilities/zod-validators";

export const intellectualPropertyAssetSchema = z.object({
  description: z.string().min(1, "Description is required"),
  acquisitionDate: nonNullDate("The acquisition date"),
  income: z.coerce.number({
    invalid_type_error: "An income is required.",
  }).gt(0, {
    message: "An income is required and must be greater than 0.",
  }),
});

export const intellectualPropertiesSchema = z.object({
  intellectualPropertyAcquired: stringBoolean(),
  assetsAcquired: z.array(intellectualPropertyAssetSchema).optional(),
}).refine(data => !(data.intellectualPropertyAcquired === "true" && (!data.assetsAcquired || !data.assetsAcquired?.length)), {
  path: ["assetsAcquired", 0],
  message: "At least one asset must be provided.",
});

export type IntellectualPropertyAssetType = z.infer<typeof intellectualPropertyAssetSchema>;
export type IntellectualPropertiesType = z.infer<typeof intellectualPropertiesSchema>;
