import type { Submission } from "~/features/submissions/api/get-submission";
import { client } from "~/lib/api-client";
import type { ClientRequestHeaders } from "~/lib/types/client-request-headers";
import type { PaginatedResponse } from "~/lib/types/paginated-response-type";
import type { Company } from "~/features/companies/api/get-companies";
import type { ModuleDTO } from "~/services/api-generated";

type GetSubmissionsParams = {
  company: Company
  module: ModuleDTO
  query?: {
    SortBy?: string
    SortOrder?: string
    PageNumber?: number
    PageSize?: number
  }
}

export function getSubmissions({ company, module, query, accessToken, userId }: GetSubmissionsParams & ClientRequestHeaders): Promise<PaginatedResponse<Submission>> {
  return client.get<PaginatedResponse<Submission>>(
    `/client/companies/${company.companyId}/modules/${module.id}/submissions`,
    accessToken,
    userId,
    { params: query },
  );
}

export async function getSubmissionsData({ company, module, accessToken, userId }: GetSubmissionsParams & ClientRequestHeaders): Promise<Submission[]> {
  const { data } = await getSubmissions({ company, module, accessToken, userId });

  return data;
}
