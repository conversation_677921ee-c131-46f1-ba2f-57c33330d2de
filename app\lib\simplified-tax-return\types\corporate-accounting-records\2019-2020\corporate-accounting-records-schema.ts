import { z } from "zod";
import { stringBoolean } from "~/lib/utilities/zod-validators";

export const accountingActivitySchema = z.object({
  description: z.string().min(1, "Description is required"),
  relatedPartyIntellectualProperty: stringBoolean(),
  nonRelatedIntellectualProperty: stringBoolean(),
  nonIntellectualProperty: stringBoolean(),
  income: z.coerce.number({
    invalid_type_error: "An income is required.",
  }).gt(0, {
    message: "An income is required and must be greater than 0.",
  }),
});

export const corporateAccountingInformationSchema = z.object({
  assessableIncomeGenerated: stringBoolean(),
  activitiesCondition: stringBoolean().optional(),
  accountingActivities: z.array(accountingActivitySchema).optional(),
})
  .refine(data => !(data.assessableIncomeGenerated === "true" && !data.activitiesCondition), {
    message: "Required.",
    path: ["activitiesCondition"],
  })
  .refine(data => !(data.assessableIncomeGenerated === "true" && data.activitiesCondition === "false" && !data.accountingActivities?.length), {
    path: ["accountingActivities", 0],
    message: "At least one activity must be provided.",
  });

export type AccountingActivityType = z.infer<typeof accountingActivitySchema>;
export type CorporateAccountingInformationType = z.infer<typeof corporateAccountingInformationSchema>;
