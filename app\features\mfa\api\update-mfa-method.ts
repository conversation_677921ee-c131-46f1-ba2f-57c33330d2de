import { z } from "zod";
import { client } from "~/lib/api-client";
import { Method, type MethodKeys } from "~/lib/auth/utils/mfa";
import type { ClientRequestHeaders } from "~/lib/types/client-request-headers";

export const mfaUpdateFormSchema = z.object({
  mfaMethod: z.enum([Method.Email, Method.Authenticator], {
    required_error: "You need to select an authentication method.",
  }),
});

export type mfaUpdateFormType = z.infer<typeof mfaUpdateFormSchema>;

export const mfaUpdateSchema = mfaUpdateFormSchema.extend({
  userId: z.string().min(1, "Required"),
});

export type mfaUpdateType = z.infer<typeof mfaUpdateSchema>;

export type MFADetails = {
  userId: string
  mfaMethod: MethodKeys
  mfaIsEnabled: boolean
  mfaAuthenticatorQRUrl: string
  mfaAuthenticatorSecret: string
  mfaEmailCodeExpiresAt: string
  mfaEmailCodeExpiresIn: number
}

export function updateMfaMethod({ data, accessToken, userId }: { data: mfaUpdateType } & ClientRequestHeaders): Promise<MFADetails> {
  return client.put(`/security/mfa/users/${userId}/method`, accessToken, userId, data);
}
