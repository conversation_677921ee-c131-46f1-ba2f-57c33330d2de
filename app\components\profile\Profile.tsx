import {
  Avatar,
  AvatarFallback,
  AvatarImage,
  Badge,
  Button,
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  cn,
} from "@netpro/design-system";
import { Form, Link, useRouteLoaderData } from "@remix-run/react"
import { CircleHelp, Inbox, KeySquare, LogOut } from "lucide-react";
import { type ReactNode, useState } from "react";
import type { RootLoaderData } from "~/lib/auth/types/session-type";

type ProfileProps = {
  alignment?: "left" | "right"
  className?: string
};

function getUserInitials(userName?: string): string {
  if (!userName) {
    // If the user name is not available, return a default value
    return "TT";
  }

  const nameArray = userName.split(" ");
  if (nameArray.length === 1) {
    return nameArray[0][0].toUpperCase();
  }

  return `${nameArray[0][0]}${nameArray[1][0]}`.toUpperCase();
}

function formatNotificationCounter(notification: number, compact = false): string {
  if (compact && notification >= 10) {
    return `${Math.floor(notification / 10)}+`;
  }

  return notification.toString();
}

export function Profile({
  alignment = "right",
  className = "",
}: ProfileProps): ReactNode {
  const rootLoader = useRouteLoaderData<RootLoaderData>("root");
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  if (!rootLoader) {
    return null;
  }

  const defaultUserData = {
    userName: "Unknown User",
    userEmail: "Unknown Email",
    profilePictureUrl: "",
    notifications: {
      hasNotifications: false,
      unreadCount: 0,
    },
  };
  const { userName, userEmail, profilePictureUrl, notifications } = rootLoader ? rootLoader.sessionData : defaultUserData;

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <div
            className={cn(`flex w-auto cursor-pointer justify-end gap-1 ${alignment === "right" ? "flex-row " : "flex-row-reverse"} ${className}`)}
          >
            <div className="flex flex-col min-w-0 justify-center text-black hover:text-primary">
              <span title={userName} className={`text-xs text-ellipsis overflow-hidden font-semibold font-inter  ${alignment === "right" ? "text-right" : "text-left"}`}>
                {userName}
              </span>
              <span title={userEmail} className={`text-xs text-ellipsis overflow-hidden font-normal font-inter ${alignment === "right" ? "text-right" : "text-left"}`}>
                {userEmail}
              </span>
            </div>

            <div className="relative inline-block">
              <Avatar>
                <AvatarImage src={profilePictureUrl} />
                <AvatarFallback className="text-base font-normal font-inter text-white bg-gray-500">
                  {getUserInitials(userName ?? userEmail)}
                </AvatarFallback>
              </Avatar>
              {notifications?.hasNotifications && (
                <Badge
                  label={formatNotificationCounter(notifications.unreadCount, true)}
                  variant="error"
                  className="absolute h-3.5 top-0 -right-1 text-xs font-semibold font-inter rounded-md py-0.5 px-1  z-10"
                />
              )}
            </div>
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-50">
          <DropdownMenuLabel>My Account</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuGroup>
            <DropdownMenuItem>
              <button type="button" onClick={() => setIsDialogOpen(true)} className="flex w-full items-center gap-2">
                <KeySquare className="size-4" />
                <span className="font-inter text-sm">Change password</span>
              </button>
            </DropdownMenuItem>
            {/* Sections hidden since no implementation is done yet. Update when necessary */}
            <DropdownMenuItem className="hidden">
              <Link to="#" className="flex w-full items-center justify-between">
                <div className="flex items-center gap-2">
                  <Inbox className="size-4" />
                  <span className="font-inter text-sm">Messages</span>
                </div>

                {notifications?.hasNotifications && (
                  <Badge
                    label={formatNotificationCounter(notifications.unreadCount)}
                    variant="error"
                    className="w-auto text-xs/3  font-semibold font-inter rounded-md py-0.5 px-1"
                  />
                )}
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem className="hidden">
              <Link to="#" className="flex w-full items-center gap-2">
                <CircleHelp className="size-4" />
                <span className="font-inter text-sm">Support</span>
              </Link>
            </DropdownMenuItem>
          </DropdownMenuGroup>
          <DropdownMenuSeparator />
          <DropdownMenuItem className="w-full">
            <Form action="/logout" method="post" className="w-full">
              <button type="submit" className="flex w-full items-center gap-2">
                <LogOut className="size-4" />
                <span className="font-inter text-sm">Log out</span>
              </button>
            </Form>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      <Dialog modal open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-xl">
          <DialogHeader>
            <DialogTitle>Change password</DialogTitle>
          </DialogHeader>
          <div className="flex flex-col gap-1">
            To change your password, please log out and follow these steps:
            <ul className="list-disc list-inside">
              <li>Click the "Login" button.</li>
              <li>Enter your email address.</li>
              <li>Click "Forgot password?"</li>
              <li>Follow the instructions to reset your password.</li>
            </ul>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => setIsDialogOpen(false)}
            >
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
