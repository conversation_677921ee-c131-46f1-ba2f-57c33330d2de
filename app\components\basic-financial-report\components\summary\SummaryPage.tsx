import { useLoaderData } from "@remix-run/react";
import type { ReactNode } from "react";
import Page from "~/components/pages/Page";
import PageContent from "~/components/pages/PageContent";
import PageHeader from "~/components/pages/PageHeader";
import { Logo } from "~/components/ui/branding/Logo";
import { SubmissionStatusNames } from "~/lib/submission/utilities/submission-status";
import type { BasicFinancialReportSummaryLoader } from "~/routes/_pdf.basic-financial-report.$id.summary";

export function SummaryPage({ children }: { children: ReactNode }): ReactNode {
  const { entityDetails: { legalEntityName, status } } = useLoaderData<BasicFinancialReportSummaryLoader>()
  const childrenReturn = () => {
    if (status === SubmissionStatusNames.Draft || status === SubmissionStatusNames.Temporal) {
      return (
        <div className="relative">
          <div className="absolute text-blue-500/30 font-extrabold transform -rotate-45 text-[250px] top-32 -right-10">DRAFT</div>
          {children}
        </div>
      )
    }

    return (
      <div className="flex flex-col gap-4 font-inter">
        {children}
      </div>
    )
  }

  return (
    <Page>
      <PageHeader>
        <div className="flex justify-between items-center">
          <h1 className="font-inter text-xl tracking-widest text-blue-500">
            {legalEntityName}
          </h1>
          <Logo className="h-8" />
        </div>

      </PageHeader>
      <PageContent>
        {childrenReturn()}
      </PageContent>
    </Page>
  );
}
