import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
} from "@netpro/design-system"
import { useLocation } from "@remix-run/react"
import { Alert<PERSON>riangle } from "lucide-react"
import type { ReactNode } from "react"
import { useBoDirColumns } from "../hooks/useBoDirColumns"
import { BoDirCell } from "./BoDirCell"
import type {
  BeneficialOwnerType,
  DirectorType,
} from "~/lib/bo-directors/utilities/bo-directors-columns"
import type { BeneficialOwnerDTO, DirectorDTO } from "~/services/api-generated"

type RowModalProps = {
  open: boolean
  handleOpenChange: (isOpen: boolean) => void
  item: DirectorDTO | BeneficialOwnerDTO
  requiredFields: string[]
  type: BeneficialOwnerType | DirectorType
}

export default function RowModal({
  open,
  handleOpenChange,
  item,
  requiredFields,
  type,
}: RowModalProps): ReactNode {
  const location = useLocation()
  const isBeneficialOwner = location.pathname.includes("bo-directors/beneficial-owner");
  const memoizedColumns = useBoDirColumns({ item, type, isBeneficialOwner });
  const boDirector = isBeneficialOwner ? item as BeneficialOwnerDTO : item as DirectorDTO;

  return (
    <Dialog modal open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-3xl [&>button]:hidden">
        <DialogHeader>
          <DialogDescription>
            {boDirector?.isIndividual ? "Individual Identity" : "Corporate Identity"}
          </DialogDescription>
          <DialogTitle>
            {`Details for ${isBeneficialOwner ? "Beneficial Owner" : "Officer"} `}
            <span className="text-blue-500">{`${boDirector?.name}`}</span>
          </DialogTitle>
        </DialogHeader>
        <Table className="mt-4">
          <TableBody>
            {memoizedColumns && memoizedColumns.map(
              ({ field, name }, index) => (
                <TableRow key={`modal-bo-dir-${field}`} className={index === 0 ? "border-t border-gray-200" : ""}>
                  <TableHead className="font-bold text-black">
                    <span className="flex items-center justify-between">
                      {name}
                      {requiredFields.includes(field) && (!(field in boDirector) || !(boDirector as any)[field]) && (
                        <AlertTriangle
                          size={16}
                          className="text-orange-700 mr-2 flex-shrink-0"
                          strokeWidth={3}
                        />
                      )}
                    </span>
                  </TableHead>
                  <TableCell key={`modal-bo-dir-row-${field}-${boDirector.id}`}>
                    <BoDirCell field={field} item={item} />
                  </TableCell>
                </TableRow>
              ),
            )}
          </TableBody>
        </Table>

        <DialogFooter>
          <Button
            type="button"
            variant="secondary"
            onClick={() => handleOpenChange(false)}
          >
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
