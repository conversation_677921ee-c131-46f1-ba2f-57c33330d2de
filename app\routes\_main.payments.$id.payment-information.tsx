import { zodResolver } from "@hookform/resolvers/zod";
import {
  <PERSON>ton,
  Combobox,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Separator,
  Spinner,
  notify,
} from "@netpro/design-system";
import type { ActionFunctionArgs, LoaderFunctionArgs, TypedResponse } from "@remix-run/node";
import { Form as RemixForm, json, redirect, useFetcher, useLoaderData, useNavigate, useParams } from "@remix-run/react";
import { ChevronRight, CreditCard, TriangleAlert } from "lucide-react";
import type { ReactNode } from "react";
import { useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { Breadcrumb } from "~/components/breadcrumbs/Breadcrumb";
import { createTransactionSchema } from "~/features/payments/api/create-transaction";
import { getPayment } from "~/features/payments/api/get-payment";
import type { SubmittedTransaction } from "~/features/payments/api/submit-transaction";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { commitSession, getSession, getSessionData } from "~/lib/auth/utils/session.server";
import { middleware } from "~/lib/middlewares.server";
import type { PaymentInformationType } from "~/lib/payments/types/payment-information-schema";
import { paymentInformationSchema } from "~/lib/payments/types/payment-information-schema";
import { PaymentProviderStatus } from "~/lib/payments/utilities/payment-provider-status";
import { PaymentStatus } from "~/lib/payments/utilities/payment-status";
import { getCountryOptions } from "~/lib/utilities/countries";
import { useScrollToError } from "~/lib/utilities/use-scroll-to-error";
import type { ClientCreatePaymentTransactionResponse } from "~/services/api-generated";
import { clientCreatePaymentTransaction } from "~/services/api-generated";

const title = "Payment information" as const;
const breadCrumbList = [
  {
    href: "/payments/pending",
    name: "Payments",
  },
  {
    href: "/",
    name: "Pending Payments",
  },
];

export const handle = {
  breadcrumb: (): JSX.Element => <Breadcrumb data={breadCrumbList} />,
  title,
};

export async function loader({ request, params }: LoaderFunctionArgs): Promise<TypedResponse<never> | {
  amount: number
}> {
  const { userId, accessToken } = await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany"], request);
  const session = await getSession(request.headers.get("Cookie"));
  const { id } = params;

  if (!id) {
    throw new Error("Payment ID is required");
  }

  const payment = await getPayment({
    paymentId: id,
    accessToken,
    userId,
  });

  if (!payment) {
    return redirect("/payments/pending");
  }

  if (payment.status !== PaymentStatus.INPROGRESS) {
    session.flash("notification", {
      variant: "info",
      message: "Payment has already been completed.",
      title: "Payment completed",
    });

    return redirect("/payments/pending", {
      headers: {
        "Set-Cookie": await commitSession(session),
      },
    });
  }

  return {
    amount: payment.amount,
  };
}

export async function action({ request, params }: ActionFunctionArgs) {
  await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany"], request);
  const { userEmail } = await getSessionData(request);
  const session = await getSession(request.headers.get("Cookie"));
  const { id: paymentId } = params;

  if (!paymentId) {
    throw new Error("Payment ID is required");
  }

  const formData = await request.formData();
  const firstName = formData.get("firstName") as string;
  const lastName = formData.get("lastName") as string;

  if (!firstName || !lastName) {
    throw new Error("First name and last name are required");
  }

  const baseUrl = process.env.APPLICATION_BASE_URL;

  if (!baseUrl) {
    throw new Error("Base URL is not set");
  }

  const transaction = {
    firstName,
    lastName,
    email: userEmail as string,
    description: "Trident Trust Payment",
    orderId: paymentId,
    paymentRedirectUrl: `${baseUrl}/payments/${paymentId}/callback`,
    cancelUrl: `${baseUrl}/payments/pending`,
    companyName: "Trident Trust Company (Nevis) Limited", // TODO: Make this dynamic based on jurisdiction
    phoneNumber: "+1**************", // TODO: Make this dynamic based on jurisdiction
    merchantEmail: "<EMAIL>", // TODO: Make this dynamic based on jurisdiction
  }
  createTransactionSchema.parse(transaction);

  const { data, error } = await clientCreatePaymentTransaction({ headers: await authHeaders(request), path: { paymentId }, body: transaction });

  if (error) {
    session.flash("notification", { title: "An error occurred", message: error.exceptionMessage || "An error occurred while processing the payment.", variant: "error" });

    return json({ success: false }, {
      headers: {
        "Set-Cookie": await commitSession(session),
      },
    });
  }

  return json({
    ...data,
    success: true,
  });
}

type ResponseType = ClientCreatePaymentTransactionResponse & { success: boolean };

export default function PaymentInformationContainer(): ReactNode {
  const { amount } = useLoaderData<typeof loader>();
  const params = useParams();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [open, setOpen] = useState(false);
  const submitFetcher = useFetcher<ResponseType>();
  const cancelFetcher = useFetcher<{ success: boolean }>();
  const navigate = useNavigate();
  const form = useForm<PaymentInformationType>({
    resolver: zodResolver(paymentInformationSchema),
    shouldFocusError: false,
    defaultValues: {
      firstName: "",
      lastName: "",
      address: "",
      city: "",
      state: "",
      zipCode: "",
      country: "",
      phoneNumber: "",
      creditCardNumber: "",
      expirationMonth: "",
      expirationYear: "",
      cvv: "",
    },
    disabled: isSubmitting,
  });
  const { formState } = form;
  const [canFocus, setCanFocus] = useState(true)
  useScrollToError(formState, canFocus, setCanFocus);

  function onError(): void {
    setCanFocus(true)
  }

  const countryOptions = useMemo(() => getCountryOptions(), []);

  function onCancel(): void {
    cancelFetcher.submit({}, {
      method: "post",
      action: `/api/payments/${params.id!}/payment-information/cancel`,
    });
  }

  function onSubmit(data: PaymentInformationType): void {
    setIsSubmitting(true);
    submitFetcher.submit({
      firstName: data.firstName,
      lastName: data.lastName,
    }, {
      method: "post",
    });
  }

  useEffect(() => {
    async function handleResponse(transactionInitResponse: ClientCreatePaymentTransactionResponse): Promise<void> {
      try {
        // Create payment FormData
        const formData = new FormData();
        formData.append("inputTotalAmount", amount.toString());
        formData.append("billing-first-name", form.getValues("firstName"));
        formData.append("billing-last-name", form.getValues("lastName"));
        formData.append("billing-phone", form.getValues("phoneNumber"));
        formData.append("billing-cc-number", form.getValues("creditCardNumber"));
        formData.append("billing-cc-cvv", form.getValues("cvv"));
        formData.append("billing-cc-exp", `${form.getValues("expirationMonth")}${form.getValues("expirationYear")}`);
        formData.append("billing-address1", form.getValues("address"));
        formData.append("billing-city", form.getValues("city"));
        formData.append("billing-state", form.getValues("state"));
        formData.append("billing-postal", form.getValues("zipCode"));
        formData.append("billing-country", form.getValues("country"));

        // Submit payment to the payment provider
        const providerResponse: {
          paymentId: string
          tokenId: string
          transactionId: string
        } = await (await fetch(transactionInitResponse.paymentProcessorResponse!.callBackUrl!, {
          method: "POST",
          body: formData,
        })).json();
        const clientResponse: SubmittedTransaction = await (await fetch(`/api/payments/${providerResponse.paymentId}/completed?tokenId=${providerResponse.tokenId}&transactionId=${providerResponse.transactionId}`)).json();
        if (clientResponse.result === PaymentProviderStatus.APPROVED) {
          // Payment was successful, redirect to the pending payments page
          notify({ variant: "success", message: "Payment completed successfully.", title: "Payment successful" });

          return navigate("/payments/pending");
        } else {
          notify({ variant: "error", message: clientResponse.resultText, title: "Payment error" });
        }
      } catch (error) {
        notify({ variant: "error", message: "An error occurred while processing the payment.", title: "Payment error" });
      } finally {
        setIsSubmitting(false);
      }
    }

    if (submitFetcher.state === "idle" && submitFetcher.data) {
      if (submitFetcher.data?.success) {
        handleResponse(submitFetcher.data);
      } else {
        setIsSubmitting(false);
      }
    }
  }, [submitFetcher, navigate, form, amount]);

  useEffect(() => {
    if (cancelFetcher.state === "idle" && cancelFetcher.data) {
      if (cancelFetcher.data.success) {
        notify({ variant: "info", message: "Payment has been cancelled.", title: "Payment cancelled" });
        navigate("/payments/pending");
      } else {
        notify({ variant: "error", message: "An error occurred while cancelling the payment.", title: "Cancellation error" });
      }
    }
  }, [cancelFetcher, navigate]);

  const availableCardYears = Array.from({ length: 11 }, (_, i) => new Date().getFullYear() + i);

  return (
    <Form {...form}>
      <RemixForm onSubmit={form.handleSubmit(onSubmit, onError)} noValidate className="w-full">
        <div className="flex flex-col w-full justify-between">
          <div className="flex flex-col space-y-2 w-full">
            <div className="grid sm:grid-cols-1 md:grid-cols-2 md:w-2/3 sm:w-full gap-2">
              <FormField
                control={form.control}
                name="firstName"
                render={({ field, fieldState }) => (
                  <FormItem className="col-span-1">
                    <FormLabel>First name *</FormLabel>
                    <FormControl>
                      <Input
                        invalid={!!fieldState.error}
                        placeholder="Cardholder's first name"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="lastName"
                render={({ field, fieldState }) => (
                  <FormItem className="col-span-1">
                    <FormLabel>Last name *</FormLabel>
                    <FormControl>
                      <Input
                        invalid={!!fieldState.error}
                        placeholder="Cardholder's last name"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <FormField
              control={form.control}
              name="address"
              render={({ field, fieldState }) => (
                <FormItem className="md:w-1/2 sm:w-full">
                  <FormLabel>Address *</FormLabel>
                  <FormControl>
                    <Input
                      invalid={!!fieldState.error}
                      placeholder="Cardholder's address"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="city"
              render={({ field, fieldState }) => (
                <FormItem className="md:w-1/3 sm:w-full pr-1">
                  <FormLabel>City *</FormLabel>
                  <FormControl>
                    <Input
                      invalid={!!fieldState.error}
                      placeholder="Cardholder's city"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="grid sm:grid-cols-1 md:grid-cols-2 md:w-2/3 sm:w-full gap-2">
              <FormField
                control={form.control}
                name="state"
                render={({ field, fieldState }) => (
                  <FormItem className="col-span-1">
                    <FormLabel>State *</FormLabel>
                    <FormControl>
                      <Input
                        invalid={!!fieldState.error}
                        placeholder="Cardholder's state"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="zipCode"
                render={({ field, fieldState }) => (
                  <FormItem className="col-span-1">
                    <FormLabel>Postal / ZIP Code *</FormLabel>
                    <FormControl>
                      <Input
                        invalid={!!fieldState.error}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <FormField
              control={form.control}
              name="country"
              render={({ field, fieldState }) => (
                <FormItem className="md:w-1/3 sm:w-full pr-1">
                  <FormLabel>Country *</FormLabel>
                  <FormControl>
                    <Combobox
                      placeholder="Select a country"
                      searchText="Search..."
                      noResultsText="No countries found."
                      items={countryOptions}
                      onChange={field.onChange}
                      value={field.value}
                      invalid={!!fieldState.error}
                      disabled={field.disabled}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="phoneNumber"
              render={({ field, fieldState }) => (
                <FormItem className="md:w-1/3 sm:w-full pr-1">
                  <FormLabel>Phone number *</FormLabel>
                  <FormControl>
                    <Input
                      invalid={!!fieldState.error}
                      placeholder="Cardholder's phone number"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="creditCardNumber"
              render={({ field, fieldState }) => (
                <FormItem className="md:w-1/2 sm:w-full pr-1">
                  <FormLabel>Credit card number *</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        invalid={!!fieldState.error}
                        placeholder="1234-1234-1234-1234"
                        className="pl-10"
                        {...field}
                      />
                      <CreditCard className="absolute left-3 top-1/2 transform -translate-y-1/2 size-5" />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="grid md:grid-cols-2 gap-2">
              <div className="flex-col space-y-2">
                <Label>Expiration *</Label>
                <div className="grid grid-cols-2 gap-1">
                  <FormField
                    control={form.control}
                    name="expirationMonth"
                    render={({ field, fieldState }) => (
                      <FormItem className="col-span-1">
                        <Select onValueChange={field.onChange} value={field.value} disabled={field.disabled}>
                          <FormControl>
                            <SelectTrigger invalid={fieldState.invalid}>
                              <SelectValue placeholder="Month" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="01">01</SelectItem>
                            <SelectItem value="02">02</SelectItem>
                            <SelectItem value="03">03</SelectItem>
                            <SelectItem value="04">04</SelectItem>
                            <SelectItem value="05">05</SelectItem>
                            <SelectItem value="06">06</SelectItem>
                            <SelectItem value="07">07</SelectItem>
                            <SelectItem value="08">08</SelectItem>
                            <SelectItem value="09">09</SelectItem>
                            <SelectItem value="10">10</SelectItem>
                            <SelectItem value="11">11</SelectItem>
                            <SelectItem value="12">12</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="expirationYear"
                    render={({ field, fieldState }) => (
                      <FormItem className="col-span-1">
                        <Select onValueChange={field.onChange} value={field.value} disabled={field.disabled}>
                          <FormControl>
                            <SelectTrigger invalid={fieldState.invalid}>
                              <SelectValue placeholder="Year" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {availableCardYears.map(year => (
                              <SelectItem key={year} value={year.toString()}>
                                {year}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
              <div className="flex-col space-y-2">
                <Label>CVV *</Label>
                <FormField
                  control={form.control}
                  name="cvv"
                  render={({ field, fieldState }) => (
                    <FormItem className="md:w-1/3 sm:w-full">
                      <FormControl>
                        <Input
                          invalid={!!fieldState.error}
                          placeholder="CVV"
                          maxLength={4}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

              </div>
            </div>
            <div className="py-2">
              <div>Trident Trust Company (Nevis) Limited</div>
              <div>Hunkins Plaza,</div>
              <div>Main Street</div>
              <div>Suite 556</div>
              <div>Charlestown, Nevis, West Indies</div>
              <div>Tel: +1**************</div>
              <div>Fax: +1**************</div>
              <div><EMAIL></div>
            </div>
          </div>
          <div className="flex justify-end py-2">
            <span className="font-semibold">
              {`For the amount of USD ${amount}`}
            </span>
          </div>
          <div>
            <Separator orientation="horizontal" />
            <div className="flex justify-between mt-4">
              <span className="text-xs text-gray-500 my-auto">This payment is processed by CX Pay</span>
              <div className="flex space-x-2">
                <Dialog open={open} onOpenChange={newOpenState => setOpen(newOpenState)}>
                  <DialogTrigger asChild>
                    <Button type="button" variant="outline" disabled={isSubmitting}>
                      Cancel
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle className="flex gap-2 items-center">
                        <TriangleAlert size={30} className="text-destructive" />
                        Are you sure you want cancel this payment?
                      </DialogTitle>
                    </DialogHeader>
                    <DialogDescription>
                      If you cancel this payment, the payment will be voided and you will need to start the payment process again.
                    </DialogDescription>
                    <DialogFooter>
                      <div className="flex flex-row items-center gap-2">
                        <Button
                          disabled={cancelFetcher.state === "submitting"}
                          type="button"
                          size="sm"
                          variant="secondary"
                          onClick={() => setOpen(false)}
                        >
                          Close
                        </Button>
                        <Button
                          type="button"
                          disabled={cancelFetcher.state === "submitting"}
                          size="sm"
                          variant="destructive"
                          className="place-self-end"
                          onClick={onCancel}
                        >
                          {cancelFetcher.state !== "idle" ? "Cancelling..." : "Yes, cancel this payment"}
                        </Button>
                      </div>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting
                    ? (
                        <>
                          Submitting
                          <Spinner className="ml-2 size-4 text-white" />
                        </>
                      )
                    : (
                        <>
                          Submit payment
                          <ChevronRight className="ml-2 size-4 text-white" />
                        </>
                      )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </RemixForm>
    </Form>
  )
}
