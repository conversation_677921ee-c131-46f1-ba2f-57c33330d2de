import { <PERSON><PERSON>, <PERSON>rollA<PERSON>, <PERSON>rollBar, Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@netpro/design-system";
import { Pencil, X } from "lucide-react";
import type { ReactNode } from "react";
import type { CashBankAccountSchemaType } from "~/lib/basic-financial-report/types/current-assets-details-schema";

type Props = {
  cashBankAccounts: (CashBankAccountSchemaType & { formArrayId: string })[]
  onSelect: (income: CashBankAccountSchemaType, index: number) => void
  onDelete: (index: number) => void
  disabled: boolean
}

export function CashBankAccountTable({
  cashBankAccounts,
  onSelect,
  onDelete,
  disabled,
}: Props): ReactNode {
  return (
    <div className="border-gray-200 border mt-4">
      <ScrollArea>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Bank Name</TableHead>
              <TableHead>Account Type</TableHead>
              <TableHead>Amount</TableHead>
              <TableHead></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {!cashBankAccounts.length && (
              <TableRow>
                <TableCell colSpan={5} className="text-center text-gray-500">
                  No assets available
                </TableCell>
              </TableRow>
            )}
            {cashBankAccounts.length > 0 && cashBankAccounts.map((cashBankAccount, index) => (
              <TableRow key={cashBankAccount.formArrayId}>
                <TableCell>{cashBankAccount.bankName}</TableCell>
                <TableCell>{cashBankAccount.accountType}</TableCell>
                <TableCell>{`$ ${cashBankAccount.amount}`}</TableCell>
                <TableCell className="flex justify-end gap-2">
                  <Button type="button" size="sm" variant="secondary" onClick={() => onSelect(cashBankAccount, index)} disabled={disabled}>
                    <Pencil className="mr-2 size-4" />
                    Edit
                  </Button>
                  <Button type="button" size="sm" variant="destructive" onClick={() => onDelete(index)} disabled={disabled}>
                    <X className="mr-2 size-4" />
                    Remove
                  </Button>
                </TableCell>
              </TableRow>
            ))}
            <TableRow>
              <TableCell className="font-semibold" colSpan={2}>TOTAL</TableCell>
              <TableCell className="font-semibold">{`$ ${cashBankAccounts.reduce((acc, cur) => acc + Number(cur.amount), 0)}`}</TableCell>
              <TableCell />
            </TableRow>
          </TableBody>
        </Table>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>
    </div>
  )
}
