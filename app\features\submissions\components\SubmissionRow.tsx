import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Trigger } from "@netpro/design-system";
import { Link, useFetcher } from "@remix-run/react";
import { ChevronRight, FileDown, Trash2, <PERSON><PERSON><PERSON><PERSON> } from "lucide-react";
import { type JSX, useEffect, useState } from "react";
import type { Submission } from "~/features/submissions/api/get-submission";
import { SubmissionStatusNames } from "~/lib/submission/utilities/submission-status";
import { formatDate } from "~/lib/utilities/format";
import { StatusBadge } from "~/features/submissions/components/StatusBadge";
import { SubmissionRowMeta } from "~/features/submissions/components/SubmissionRowMeta";
import type { SubmissionStatus } from "~/services/api-generated";

type SubmissionRowProps = {
  submission: Submission
  deleteDraftAction?: boolean
  moduleUrl: string
  continuePageName: string
};
type ActionReturnType = {
  errors: {
    managerDeleteDraft: string
  }
} | {
  success: boolean
}

export function SubmissionRow({ submission, deleteDraftAction = false, moduleUrl, continuePageName }: SubmissionRowProps): JSX.Element {
  const [open, setOpen] = useState(false);
  const { id, financialYear, statusText, submittedAt, updatedAt, createdAt, isPaid, invoiceId } = submission;
  const status = statusText as SubmissionStatus;
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const fetcher = useFetcher<ActionReturnType>({ key: `DraftDelete-${id}` });

  useEffect(() => {
    if (fetcher.data && "errors" in fetcher.data) {
      setErrorMessage(fetcher.data.errors.managerDeleteDraft);
    }
  }, [fetcher.data, id]);

  return (
    <div
      className="flex items-center justify-between py-3 border-2 border-transparent hover:border-blue-600 group px-5 transition-all duration-200 rounded-md"
    >
      <div className="flex items-end space-x-4">
        <div>
          <p className="text-lg font-inter font-semibold group-hover:text-foreground">
            Reporting Year:
            {" "}
            <span className="text-blue-600">
              {financialYear}
            </span>
          </p>
          <div className="flex flex-row gap-2 divide-x-2">
            {status === SubmissionStatusNames.Submitted && submittedAt && (
              <SubmissionRowMeta label="Date submitted" value={formatDate(submittedAt)} />
            )}
            {(status === SubmissionStatusNames.Draft || status === SubmissionStatusNames.Revision) && updatedAt && (
              <SubmissionRowMeta label="Last activity" value={formatDate(updatedAt)} />
            )}
            <SubmissionRowMeta label="Date started" value={formatDate(createdAt)} />
          </div>
        </div>
      </div>

      <div className="flex items-center gap-4">
        <div className="flex items-center gap-1">
          {!isPaid && status === SubmissionStatusNames.Submitted && (
            <Badge variant="danger">Pending Payment</Badge>
          )}
          {isPaid && (
            <Badge variant="success">Paid</Badge>
          )}
          <StatusBadge status={status} />
        </div>

        {status !== SubmissionStatusNames.Draft && status !== SubmissionStatusNames.Revision
          ? (
              <>
                <Button variant="outline" size="sm" asChild>
                  <Link to={`/${moduleUrl}/${id}/summary`} target="_blank">
                    <FileDown className="text-blue-500 size-4 mr-2" />
                    Submission
                  </Link>
                </Button>
                {invoiceId && (
                  <Button variant="outline" size="sm" asChild>
                    <Link to={`/invoices/${invoiceId}/file`} target="_blank">
                      <FileDown className="text-blue-500 size-4 mr-2" />
                      Invoice
                    </Link>
                  </Button>
                )}
              </>
            )
          : (
              <>
                {deleteDraftAction && (
                  <Dialog open={open} onOpenChange={newOpenState => setOpen(newOpenState)}>
                    <DialogTrigger asChild>
                      <Button variant="link" name="intent" value="removeUser" className="flex gap-1.5 items-center text-sm text-gray-500">
                        Remove draft
                        <Trash2 size={16} className="text-gray-500" />
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle className="flex gap-5 items-center pb-5">
                          <TriangleAlert size={30} className="text-red-500" />
                          Are you sure you want to delete draft?
                        </DialogTitle>
                      </DialogHeader>
                      {errorMessage && <Alert title={errorMessage} variant="error" />}
                      <DialogFooter>
                        <fetcher.Form
                          method="post"
                          action={`/${moduleUrl}/${id}/delete`}
                        >
                          <fieldset disabled={fetcher.state === "submitting"} className="flex flex-row items-center gap-2">
                            <Button type="button" size="sm" variant="secondary" onClick={() => setOpen(false)}>Cancel</Button>
                            <input type="hidden" name="submissionId" value={id} />
                            <Button size="sm" variant="destructive" className="place-self-end" type="submit">
                              {fetcher.state !== "idle" ? "Removing..." : "Yes, remove this draft"}
                            </Button>
                          </fieldset>
                        </fetcher.Form>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                )}
                <Button variant="link" asChild>
                  <Link to={`/${moduleUrl}/${id}/${continuePageName}`} className="gap-1.5 flex items-center">
                    Continue draft
                    <ChevronRight className="size-4" />
                  </Link>
                </Button>
              </>
            )}
      </div>
    </div>
  );
}
