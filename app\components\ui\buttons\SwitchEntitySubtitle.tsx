import type { JSX } from "react";
import type { SwitchEntityTitleProps } from "./SwitchEntityTitle";

export function SwitchEntitySubtitle({ prefix, children }: SwitchEntityTitleProps): JSX.Element {
  const title = prefix ? `${prefix}: ${children}` : `${children}`;

  return (
    <h4 className="text-blue-50 text-xs font-inter flex gap-1.5 grow-0" title={title}>
      {prefix && (
        <span className="text-left">
          {prefix}
          :
        </span>
      )}
      <span className="text-ellipsis overflow-hidden font-semibold">{children}</span>
    </h4>
  );
}
