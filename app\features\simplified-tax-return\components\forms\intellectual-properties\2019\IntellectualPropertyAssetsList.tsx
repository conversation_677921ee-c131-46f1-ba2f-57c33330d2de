import { Scroll<PERSON><PERSON>, ScrollBar } from "@netpro/design-system";
import type { JSX } from "react";
import { IntellectualPropertyAssetRow } from "./IntellectualPropertyAssetRow";
import type { IntellectualPropertyAssetType } from "~/lib/simplified-tax-return/types/intellectual-property/2019/intellectual-property-schema";

type IntellectualPropertyAssetsListProps = {
  assets: (IntellectualPropertyAssetType & { formArrayId: string })[]
  onSelect: (asset: IntellectualPropertyAssetType, index: number) => void
}

export function IntellectualPropertyAssetsList({ assets, onSelect }: IntellectualPropertyAssetsListProps): JSX.Element {
  return (
    <div className="border-gray-200 border mt-4 rounded-md shadow-sm">
      <ScrollArea>
        {!assets.length && (
          <div className="p-4 text-center">
            <p>No assets added</p>
          </div>
        )}
        {assets && assets.map((asset, index) => (
          <IntellectualPropertyAssetRow asset={asset} onClick={onSelect} index={index} key={asset.formArrayId} />
        ))}
        <ScrollBar orientation="horizontal" />
      </ScrollArea>
    </div>
  )
}
