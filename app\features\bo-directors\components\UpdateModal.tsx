import {
  Button,
  Combobox,
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  Textarea,
} from "@netpro/design-system"
import { useFetcher, useLocation } from "@remix-run/react"
import { AlertTriangle } from "lucide-react"
import { useState } from "react"
import { z } from "zod"
import type { BeneficialOwnerDTO, DirectorDTO } from "~/services/api-generated"

type UpdateModalProps = {
  open: boolean
  handleOpenChange: (isOpen: boolean) => void
  selectedItem: DirectorDTO | BeneficialOwnerDTO | null
}

export default function UpdateModal({
  open,
  handleOpenChange,
  selectedItem,
}: UpdateModalProps): JSX.Element {
  const location = useLocation()
  const isBeneficialOwner = location.pathname.includes("beneficial-owner")
  const fetcher = useFetcher()
  const requestTypes = isBeneficialOwner
    ? [
        { value: "MissingBeneficialOwners", label: "Missing Beneficial Owners Info" },
        { value: "ChangeOfBeneficialOwners", label: "Change of Beneficial Owners" },
        { value: "ChangeOfBeneficialOwnersAddress", label: "Change of Beneficial Owners Address" },
        { value: "ChangeOfBeneficialOwnersParticulars", label: "Change of Beneficial Owners Particulars" },
        { value: "OtherUpdateOfBeneficialOwners", label: "Other Update for Beneficial Owners" },
      ]
    : [
        { value: "MissingDirectors", label: "Missing Directors Info" },
        { value: "ChangeOfDirectors", label: "Change of Directors" },
        { value: "ChangeOfDirectorsAddress", label: "Change of Directors Address" },
        { value: "ChangeOfDirectorsParticulars", label: "Change of Directors Particulars" },
        { value: "OtherUpdateOfDirectors", label: "Other Update for Directors" },
      ];
  const formSchema = z.object({
    uniqueRelationId: z.string(),
    updateRequestType: z.string(),
    updateRequestComments: z.string().min(1, "Comments are required."),
  });

  type FormState = z.infer<typeof formSchema>;

  const [formState, setFormState] = useState<FormState>({
    uniqueRelationId: selectedItem?.id || "",
    updateRequestType: requestTypes[0].value,
    updateRequestComments: "",
  });
  const [error, setError] = useState<string | null>(null);
  const handleSubmit = (): void => {
    if (!selectedItem) {
      return;
    }

    const result = formSchema.safeParse(formState);

    if (result.success) {
      const formData = new FormData();
      formData.append("updateRequestType", result.data.updateRequestType);
      formData.append("updateRequestComments", result.data.updateRequestComments);
      formData.append("uniqueRelationId", selectedItem.id!);

      fetcher.submit(formData, {
        method: "post",
        action: `/api/bo-directors/${isBeneficialOwner ? "beneficial-owner" : "director"}/${selectedItem.id}/request-update`,
      });

      setFormState({
        uniqueRelationId: selectedItem?.id || "",
        updateRequestType: requestTypes[0].value,
        updateRequestComments: "",
      });
      setError(null);
      handleOpenChange(false);
    } else {
      setError(result.error.errors[0].message);
    }
  };
  const handleDismiss = (): void => {
    setFormState({
      uniqueRelationId: selectedItem?.id || "",
      updateRequestType: requestTypes[0].value,
      updateRequestComments: "",
    });
    setError(null);
    handleOpenChange(false);
  }

  return (
    <Dialog modal open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-md [&>button]:hidden">
        <DialogHeader>
          <div className="flex items-center gap-2 mb-2">
            <AlertTriangle className="text-orange-700" />
            <DialogTitle>Are you sure you want to request an update?</DialogTitle>
          </div>
          <DialogDescription className="text-sm font-semibold text-muted-foreground">
            Type of request *
          </DialogDescription>

          <Combobox
            items={requestTypes}
            value={formState.updateRequestType}
            onChange={(newValue) => {
              setFormState(prev => ({ ...prev, updateRequestType: newValue || "" }));
            }}
            placeholder="Select an option"
          />
        </DialogHeader>

        <Textarea
          value={formState.updateRequestComments}
          onChange={e => setFormState(prev => ({ ...prev, updateRequestComments: e.target.value }))}
          placeholder="Enter your update request details here..."
          className="min-h-[100px]"
        />
        {error && <p className="text-red-500 mt-2 text-sm font-semibold">{error}</p>}
        <DialogFooter className="flex justify-end gap-2">
          <Button
            type="button"
            variant="secondary"
            onClick={handleDismiss}
          >
            Cancel
          </Button>
          <Button type="button" variant="destructive" onClick={handleSubmit}>
            Submit
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
