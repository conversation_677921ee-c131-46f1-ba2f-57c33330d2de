import type { ActionFunctionArgs, LoaderFunctionArgs, MetaFunction, TypedResponse } from "@remix-run/node";
import { Link, Form as RemixForm, redirect, useSubmit } from "@remix-run/react";
import { useForm } from "react-hook-form";
import type { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Button,
  Checkbox,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@netpro/design-system";
import type { ReactNode } from "react";
import { commitSession, getSession } from "~/lib/auth/utils/session.server";
import { type TermsConditionsType, termsConditionsSchema } from "~/features/terms-conditions/schemas/terms-conditions-schema";
import { middleware } from "~/lib/middlewares.server";
import { acceptTermsConditions, getTermsConditionsStatus } from "~/services/api-generated";
import { authHeaders } from "~/lib/auth/utils/auth-headers";

const title = "Terms & Privacy Policy" as const;

export const meta: MetaFunction = () => [
  { title },
  { name: "Terms & Privacy Policy", content: title },
];

export async function loader({ request }: LoaderFunctionArgs) {
  const { userId } = await middleware(["auth", "mfa"], request);
  const session = await getSession(request.headers.get("Cookie"));
  const { data, error } = await getTermsConditionsStatus({
    headers: await authHeaders(request),
    path: {
      userId,
    },
  });

  if (error) {
    throw new Error("Error getting terms and conditions status");
  }

  if (data.isAccepted) {
    session.set("termsAndConditionsAccepted", data.isAccepted);

    return redirect("/dashboard", {
      headers: {
        "Set-Cookie": await commitSession(session),
      },
    });
  } else {
    return null;
  }
}

export async function action({ request }: ActionFunctionArgs): Promise<TypedResponse<never>> {
  const { userId } = await middleware(["auth"], request);
  const session = await getSession(request.headers.get("Cookie"));
  const formData = await request.formData();
  const data = JSON.parse(formData.get("data") as string) as TermsConditionsType;
  const confirmations = termsConditionsSchema.parse(data);
  const { data: termsConditionsStatus, error } = await getTermsConditionsStatus({ headers: await authHeaders(request), path: {
    userId,
  } });

  if (error) {
    throw new Error("Failed to get terms and conditions status");
  }

  if (termsConditionsStatus.isAccepted) {
    session.set("termsAndConditionsAccepted", true);
  }

  if (confirmations.acceptTerms && confirmations.confirmPrivacyPolicy) {
    const { error } = await acceptTermsConditions({
      headers: await authHeaders(request),
      path: {
        userId,
      },
      body: {
        version: "1.0", // TODO: This should be dynamic later
      },
    });

    if (error) {
      throw new Error("Failed to accept terms and conditions");
    }

    session.set("termsAndConditionsAccepted", true);
  }

  return redirect("/dashboard", {
    headers: {
      "Set-Cookie": await commitSession(session),
    },
  });
}

export default function TermsAndConditions(): ReactNode {
  const submit = useSubmit();
  const form = useForm<TermsConditionsType>({
    resolver: zodResolver(termsConditionsSchema),
  });

  function onSubmit(data: z.infer<typeof termsConditionsSchema>): void {
    submit({ data: JSON.stringify(data) }, { method: "POST" });
  }

  return (
    <div className="mt-5">
      <div>
        <h1 className="font-inter text-2xl font-semibold">Terms & Privacy Policy</h1>
        <div className="flex flex-col gap-2 mt-2">
          <p>
            To get started, please take a moment to review and accept our Terms and Conditions and Privacy Policy.
          </p>
          <p>
            These documents outline your rights and responsibilities as a client, as well as how we collect and use
            your personal information.
          </p>
        </div>
      </div>
      <Form {...form}>
        <RemixForm onSubmit={form.handleSubmit(onSubmit)} className="mt-4 space-y-3" noValidate>
          <FormField
            control={form.control}
            name="acceptTerms"
            render={({ field, fieldState }) => (
              <FormItem>
                <div className="gap-x-3 flex">
                  <FormControl className="mt-1">
                    <Checkbox
                      invalid={!!fieldState.error}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <FormLabel className="text-md">
                    I accept the
                    {" "}
                    <Link className="text-primary hover:underline" to="/terms" target="_blank">Terms and Conditions</Link>
                    {" "}
                    *
                  </FormLabel>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="confirmPrivacyPolicy"
            render={({ field, fieldState }) => (
              <FormItem>
                <div className="gap-x-3 flex">
                  <FormControl className="mt-1">
                    <Checkbox
                      invalid={!!fieldState.error}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <FormLabel className="text-md">
                    I confirm that I have read and understand
                    {" "}
                    <Link className="text-primary hover:underline" to="/privacy-policy" target="_blank">Trident's Privacy Policy</Link>
                    {" "}
                    *
                  </FormLabel>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
          <p>
            Upon selecting both options and clicking
            {" "}
            <span className="font-semibold">"Continue"</span>
            , you will be automatically directed to your account.
          </p>
          <p>
            Thank you for choosing Trident Trust.
          </p>
          <div className="flex flex-row justify-end w-full pt-5">
            <Button type="submit" className="rounded-none h-8 bg-[#0067b8] font-normal">Continue</Button>
          </div>
        </RemixForm>
      </Form>
    </div>
  )
}
