import { z } from "zod";

export const termsConditionsSchema = z.object({
  acceptTerms: z.boolean({
    required_error: "You must accept the terms and conditions.",
  }).refine(value => value === true, {
    message: "You must accept the terms and conditions.",
  }),
  confirmPrivacyPolicy: z.boolean({
    required_error: "You must confirm the privacy policy.",
  }).refine(value => value === true, {
    message: "You must confirm the privacy policy.",
  }),
});

export type TermsConditionsType = z.infer<typeof termsConditionsSchema>;
