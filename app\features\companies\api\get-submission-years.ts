import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { client } from "~/lib/api-client";
import type { ClientRequestHeaders } from "~/lib/types/client-request-headers";
import type { Company } from "~/features/companies/api/get-companies";
import type { ModuleDTO } from "~/services/api-generated";

export type AvailableYears = {
  years: number[]
  legalEntityId: string
  moduleId: string
};

type GetSubmissionYearsParams = {
  company: Company
  module: ModuleDTO
};

export async function getSubmissionYears({ company, module, accessToken, userId }: GetSubmissionYearsParams & ClientRequestHeaders): Promise<number[]> {
  const { years } = await client.get<AvailableYears>(
    `/client/companies/${company.companyId}/modules/${module.id as string}/submission-years`,
    accessToken,
    userId,
  );

  return years;
}

type RequireYearParams = {
  request: LoaderFunctionArgs["request"] | ActionFunctionArgs["request"]
}
export async function requireYear({ request }: RequireYearParams): Promise<{ year: number }> {
  const formData = await request.formData();
  const { year }: { year: string } = JSON.parse(formData.get("data") as string);

  if (!year) {
    throw new Error("Year not provided");
  }

  return { year: Number.parseInt(year) };
}
