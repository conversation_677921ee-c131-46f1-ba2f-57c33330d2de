import { authHeaders } from "~/lib/auth/utils/auth-headers";
import type { ModuleDTO } from "~/services/api-generated";
import { clientGetCompanyModules } from "~/services/api-generated";

export async function requireActiveModule({ request, key, companyId }: { request: Request, key: string, companyId: string }): Promise<{
  module: ModuleDTO
}> {
  const { data, error } = await clientGetCompanyModules({
    headers: await authHeaders(request),
    path: { companyId },
  });

  if (error || !data) {
    if (error) {
      // Log the API error
      console.error(`Failed to retrieve company modules for company ${companyId}`, error);
    }

    throw new Response("Module is not found or disabled", { status: 412 });
  }

  const { modules } = data;
  const module = modules?.find(module => module.key === key && module.isActive);

  if (!module) {
    throw new Error("Module is not found or disabled");
  }

  return { module };
}
