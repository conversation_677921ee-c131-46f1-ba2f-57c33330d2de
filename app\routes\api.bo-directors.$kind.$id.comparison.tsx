import type { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import type { ErrorResponse } from "~/lib/types/error-response";
import { middleware } from "~/lib/middlewares.server";
import { clientGetBeneficialOwnerForComparison, clientGetDirectorForComparison } from "~/services/api-generated";
import { authHeaders } from "~/lib/auth/utils/auth-headers";

export async function loader({ params, request }: LoaderFunctionArgs) {
  await middleware([
    "auth",
    "mfa",
    "terms",
    "requireMcc",
    "requireCompany",
    "requireBoDirModule",
  ], request);
  const { id, kind } = params;

  if (!id) {
    throw new Response("Missing id", { status: 400 });
  }

  if (!kind || !["beneficial-owner", "director"].includes(kind)) {
    throw new Response("Invalid officer type", { status: 400 });
  }

  let service;
  if (kind === "beneficial-owner") {
    service = clientGetBeneficialOwnerForComparison;
  } else {
    service = clientGetDirectorForComparison;
  }

  const { data: comparisonData, error } = await service({
    headers: await authHeaders(request),
    path: { relationId: id },
  });

  if (error) {
    console.error("Error fetching comparison data:", error);

    throw json<ErrorResponse>({
      success: false,
      error: `Error fetching comparison data: ${error instanceof Error ? error.message : String(error)}`,
    }, { status: 500 });
  }

  if (comparisonData === null) {
    return json({ comparisonData: null });
  }

  return json({
    comparisonData: {
      currentVersion: comparisonData.currentVersion,
      priorVersion: comparisonData.priorVersion,
    },
  });
}
