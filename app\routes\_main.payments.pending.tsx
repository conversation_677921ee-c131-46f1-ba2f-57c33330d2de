import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { Button, Combobox, Form, FormControl, FormField, FormItem, FormMessage, Separator } from "@netpro/design-system";
import type { ActionFunctionArgs, LoaderFunctionArgs, TypedResponse } from "@remix-run/node";
import { Form as RemixForm, json, redirect, useFetcher, useLoaderData, useSearchParams } from "@remix-run/react";
import { ChevronRight, FilterX } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { Breadcrumb } from "~/components/breadcrumbs/Breadcrumb";
import { CenteredMessage } from "~/components/errors/CenteredMessage";
import type { Company } from "~/features/companies/api/get-companies";
import { getCompanies } from "~/features/companies/api/get-companies";
import { createPayment, createPaymentSchema, type createPaymentType } from "~/features/payments/api/create-payment";
import { PaymentRow } from "~/features/payments/components/PaymentRow";
import { PendingSubmissionRow } from "~/features/payments/components/PendingSubmissionRow";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { commitSession, getSession } from "~/lib/auth/utils/session.server";
import { middleware } from "~/lib/middlewares.server";
import type { paymentsFilterSchemaType } from "~/lib/payments/types/payment-schema";
import { paymentsFilterSchema } from "~/lib/payments/types/payment-schema";
import { InvoiceStatus } from "~/lib/payments/utilities/invoice-status";
import { SubmissionStatusNames } from "~/lib/submission/utilities/submission-status";
import type { InvoiceDTOPaginatedResponse, ListSubmissionDTO } from "~/services/api-generated";
import { clientGetInvoices, clientGetMasterClientSubmissions } from "~/services/api-generated";

const title = "Pending Payments" as const;
const breadCrumbList = [
  {
    href: "/",
    name: "Payments",
  },
];

export const handle = {
  breadcrumb: (): JSX.Element => <Breadcrumb data={breadCrumbList} />,
  title,
};

export async function action({ request }: ActionFunctionArgs): Promise<TypedResponse<{
  error: string
}>> {
  const { userId, accessToken, company } = await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany"], request);
  const session = await getSession(request.headers.get("Cookie"));
  const formData = await request.formData();
  const data = JSON.parse(formData.get("data") as string) as { payments: string[], currencyId: string };
  const payments = {
    invoiceIds: data.payments,
    currencyId: data.currencyId,
    legalEntityId: company.companyId,
  } as createPaymentType;
  createPaymentSchema.parse(payments);

  try {
    const createdPayment = await createPayment({
      data: payments,
      accessToken,
      userId,
    });

    return redirect(`/payments/${createdPayment.id}/payment-information`);
  } catch (error) {
    if (error instanceof Response) {
      session.flash("notification", {
        variant: "error",
        title: "Failed to create payment",
        message: await error.text(),
      });

      return json({ error: "Failed to create payment", success: false }, {
        headers: { "Set-Cookie": await commitSession(session) },
      });
    } else {
      session.flash("notification", {
        variant: "error",
        title: "Failed to create payment",
        message: "An error occurred while creating the payment",
      });

      return json({ error: "Failed to create payment", success: false }, {
        headers: { "Set-Cookie": await commitSession(session) },
      });
    }
  }
}

export async function loader({ request }: LoaderFunctionArgs): Promise<{
  payments: InvoiceDTOPaginatedResponse
  companies: Company[]
  unpaidSubmissions: ListSubmissionDTO[]
}> {
  const { userId, accessToken, masterClient } = await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany"], request);
  let filters: {
    companyId?: string
    financialYear?: string
  } = {};
  const url = new URL(request.url);
  const searchParams = Object.fromEntries(url.searchParams.entries());

  if (searchParams) {
    // Validate params
    paymentsFilterSchema.parse(searchParams);
    // Set filters
    filters = {
      companyId: searchParams.company,
      financialYear: searchParams.financialYear,
    }
  }

  const { data: invoices, error: invoiceError } = await clientGetInvoices({
    headers: await authHeaders(request),
    query: {
      ...filters,
      MasterClientId: masterClient.masterClientId,
      PageSize: 10000, // Remove this when pagination is implemented
      Status: InvoiceStatus.UNPAID,
    },
  });

  if (invoiceError || !invoices) {
    console.error("Error fetching invoices:", invoiceError);
    throw new Response("Failed to fetch invoices", { status: 500 });
  }

  const { companies } = await getCompanies({
    masterClientId: masterClient.masterClientId as string,
    accessToken,
    userId,
    params: {
      includeInactive: true,
    },
  });
  const { data, error } = await clientGetMasterClientSubmissions({
    headers: await authHeaders(request),
    path: {
      masterClientId: masterClient.masterClientId as string,
    },
    query: {
      IsPaid: false,
      HasInvoice: false,
      LegalEntityIds: filters.companyId ? [filters.companyId] : [],
      FinancialYears: filters.financialYear ? [Number(filters.financialYear)] : [],
      SubmissionStatuses: [SubmissionStatusNames.Submitted, SubmissionStatusNames.Revision],
    },
  });

  if (error) {
    console.error("Error fetching unpaid submissions:", error);
    throw new Error("Failed to fetch unpaid submissions");
  }

  return { payments: invoices, companies, unpaidSubmissions: data.data || [] };
}

export default function PendingPayments(): JSX.Element {
  const { payments, companies, unpaidSubmissions } = useLoaderData<typeof loader>();
  const [totalSelectedValue, setTotalSelectedValue] = useState<number>(0);
  const [selectedPayments, setSelectedPayments] = useState<string[]>([]);
  const [currencyId, setCurrencyId] = useState<string | undefined>();
  const [searchParams, setSearchParams] = useSearchParams();
  const fetcher = useFetcher();
  const form = useForm<paymentsFilterSchemaType>({
    resolver: zodResolver(paymentsFilterSchema),
    defaultValues: {
      company: searchParams.get("company") ?? "all",
      financialYear: searchParams.get("financialYear") ?? "all",
    },
  });
  const companiesOptions = companies.map(company => ({
    value: company.companyId,
    label: company.companyName,
  }));
  companiesOptions.unshift({ value: "all", label: "All companies" });
  // Generate financial years options from 2019 to last year
  const availableFinancialYears = Array.from({ length: new Date().getFullYear() - 2019 }, (_, i) => 2019 + i);
  const financialYearsOptions = [
    { value: "all", label: "All financial years" },
    ...availableFinancialYears.map(year => ({ value: year.toString(), label: year.toString() })),
  ];

  function onSelect(id: string, amount: number | undefined, checked: boolean, currency: string | undefined): void {
    if (checked) {
      setSelectedPayments([...selectedPayments, id]);
      setTotalSelectedValue(totalSelectedValue + (amount || 0));
      setCurrencyId(currency);
    } else {
      const newPayments = selectedPayments.filter(paymentId => paymentId !== id);
      setSelectedPayments(newPayments);
      setTotalSelectedValue(totalSelectedValue - (amount || 0));
      if (newPayments.length === 0) {
        setCurrencyId(undefined);
      }
    }
  }

  function handleSubmitFilter(data: paymentsFilterSchemaType): void {
    setSearchParams((prev) => {
      if (data.company === "all" || !data.company) {
        prev.delete("company");
      } else if (data.company) {
        prev.set("company", data.company);
      }

      if (data.financialYear === "all" || !data.financialYear) {
        prev.delete("financialYear");
      } else if (data.financialYear) {
        prev.set("financialYear", data.financialYear);
      }

      return prev;
    });
    setTotalSelectedValue(0);
    setSelectedPayments([]);
    setCurrencyId(undefined);
  }

  function clearFilters(): void {
    form.setValue("company", "all");
    form.setValue("financialYear", "all");
    form.handleSubmit(handleSubmitFilter)();
  }

  function handleSubmitSelection(): void {
    fetcher.submit({ data: JSON.stringify({
      payments: selectedPayments,
      currencyId,
    }) }, {
      method: "post",
    });
  }

  return (
    <div className="flex flex-col w-full justify-between">
      <div className="px-4 py-1">
        <Form {...form}>
          <RemixForm noValidate>
            <div className="grid md:grid-cols-4 gap-2">
              <FormField
                control={form.control}
                name="company"
                render={({ field }) => (
                  <FormItem>
                    <FormControl className="w-full">
                      <Combobox
                        placeholder="All companies"
                        searchText="Search for a companies..."
                        noResultsText="No companies found."
                        items={companiesOptions}
                        onChange={(value) => {
                          field.onChange(value);
                          form.handleSubmit(handleSubmitFilter)();
                        }}
                        value={field.value}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="financialYear"
                render={({ field }) => (
                  <FormItem>
                    <FormControl className="w-full">
                      <Combobox
                        placeholder="All financial years"
                        searchText="Search for a financial year..."
                        noResultsText="No financial years found."
                        items={financialYearsOptions}
                        onChange={(value) => {
                          field.onChange(value);
                          form.handleSubmit(handleSubmitFilter)();
                        }}
                        value={field.value}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div>
                <Button type="button" onClick={clearFilters} className="h-9">
                  <FilterX className="mr-2 size-4" />
                  Reset filters
                </Button>
              </div>
              {payments.data && payments.data.length > 0 && (
                <div className="pr-5">
                  <div className="text-end">
                    <h3 className="text-blue-700 font-semibold w-full">Total selected value</h3>
                  </div>
                  <div className="text-end">
                    <h3 className="font-semibold w-full text-sm">
                      {`USD ${totalSelectedValue}`}
                    </h3>
                  </div>
                </div>
              )}
            </div>
          </RemixForm>
        </Form>
        <div className="mt-8">
          {payments.data && payments.data.length > 0 && (
            <>
              <h2 className="text-lg font-semibold text-gray-500 my-2">Unpaid Invoices</h2>
              {payments.data.map(payment => (
                <PaymentRow payment={payment} key={payment.id} onSelect={onSelect} currencyId={currencyId} selectedPayments={selectedPayments} />
              ))}
            </>
          )}
          {payments.data && payments.data.length > 0 && unpaidSubmissions.length > 0 && (<Separator orientation="horizontal" className="my-5" />)}
          {unpaidSubmissions.length > 0 && (
            <>
              <h2 className="text-lg font-semibold text-gray-500 my-2">Submission fee included in annual license</h2>
              {unpaidSubmissions.length > 0 && unpaidSubmissions.map(submission => (
                <PendingSubmissionRow submission={submission} key={submission.id} />
              ))}
            </>
          )}
          {payments.data && payments.data.length === 0 && unpaidSubmissions.length === 0 && (
            <CenteredMessage title="No pending payments found" />
          )}
        </div>
      </div>
      {payments.data && payments.data.length > 0 && (
        <div>
          <Separator orientation="horizontal" />
          <div className="flex justify-end mt-4">
            <Button onClick={handleSubmitSelection} disabled={selectedPayments.length === 0}>
              Proceed to Payment
              <ChevronRight className="ml-2 size-4 text-white" />
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
