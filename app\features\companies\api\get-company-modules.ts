import type { ClientRequestHeaders } from "~/lib/types/client-request-headers";
import { client } from "~/lib/api-client";
import type { CompanyModuleDTO } from "~/services/api-generated";

type Response = {
  modules: CompanyModuleDTO[]
} | {
  type: string
  title: string
  status: number
  errors: Record<string, string[]>
  traceId: string
}

export function getCompanyModules(
  { accessToken, userId, companyId }: { companyId: string } & ClientRequestHeaders,
): Promise<Response> {
  return client.get<Response>(
    `/client/companies/${companyId}/modules`,
    accessToken,
    userId,
  );
}
