import type { LucideIcon } from "lucide-react";
import { Search } from "lucide-react";
import { Separator } from "@netpro/design-system";
import type { ReactNode } from "react";

type CenteredMessageProps = {
  title: string
  subtitle?: string
  children?: ReactNode
  IconComponent?: LucideIcon
}

export function CenteredMessage({ title, subtitle, IconComponent = Search, children }: CenteredMessageProps): ReactNode {
  return (
    <div className="flex flex-col items-center justify-center min-h-96 gap-6 max-w-3xl mx-auto">
      <div className="flex flex-col items-center justify-center">
        <IconComponent className="w-12 h-12 text-gray-300" />
        {subtitle
          ? (
              <div className="flex flex-col items-center gap-1 mt-4">
                <p className="text-gray-600 font-semibold text-xl">{title}</p>
                {subtitle && <p className="text-gray-400 text-base">{subtitle}</p>}
              </div>
            )
          : (
              <p className="text-gray-600 font-semibold text-xl mt-4">{title}</p>
            )}
      </div>
      {Boolean(children) && (
        <>
          <Separator />
          <div className="flex gap-4 flex-row items-center">
            {children}
          </div>
        </>
      )}
    </div>
  )
}
