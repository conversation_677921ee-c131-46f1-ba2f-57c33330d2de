import { <PERSON><PERSON>, Form, FormControl, FormField, FormItem, FormLabel } from "@netpro/design-system";
import { Plus } from "lucide-react";
import type { ReactNode } from "react";
import { useEffect, useMemo, useState } from "react";
import { Form as RemixForm, useFetcher } from "@remix-run/react";
import { useFieldArray, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { BusinessActivitiesList } from "./BusinessActivitiesList";
import { BusinessActivityDialog } from "./BusinessActivityDialog";
import { useSubmission } from "~/lib/submission/context/use-submission";
import { ValidationAlert } from "~/components/errors/ValidationAlert";
import { Pages } from "~/lib/simplified-tax-return/utilities/form-pages";
import { useScrollToError } from "~/lib/utilities/use-scroll-to-error";
import { type BusinessActivitiesFormType, type BusinessActivityType, businessActivitiesFormSchema, businessActivitySchema } from "~/lib/simplified-tax-return/types/business-activity/2019-2024/business-activity-schema";
import { WideContainer } from "~/components/ui/utilities/WideContainer";

export function BusinessActivities(): ReactNode {
  // TODO: financialYear, according to the type definition, can be undefined. This should be handled.
  const { submissionData, financialYear } = useSubmission();
  const data = useMemo(() => submissionData[Pages.BUSINESS_ACTIVITIES] as BusinessActivitiesFormType, [submissionData]);
  const [open, setOpen] = useState(false);
  const [businessActivity, setBusinessActivity] = useState<BusinessActivityType | undefined>();
  const [activityIndex, setActivityIndex] = useState<number | undefined>();
  const form = useForm<BusinessActivitiesFormType>({
    resolver: zodResolver(businessActivitiesFormSchema),
    shouldFocusError: false,
    defaultValues: {
      activities: [],
    },
  });
  const businessActivityForm = useForm<BusinessActivityType>({
    resolver: zodResolver(businessActivitySchema),
    defaultValues: {
      otherActivity: "",
      from: new Date(`${financialYear}-01-01 00:00:00`),
      to: new Date(`${financialYear}-12-31 00:00:00`),
    },
  });
  const {
    fields,
    append,
    remove,
    update,
  } = useFieldArray({
    control: form.control,
    name: "activities",
    keyName: "formArrayId",
  });
  const { reset, formState } = form;
  const [canFocus, setCanFocus] = useState(true)
  useScrollToError(formState, canFocus, setCanFocus);

  function onError(): void {
    setCanFocus(true)
  }

  useEffect(() => {
    reset(data, { keepDefaultValues: true });
  }, [data, reset]);

  function addBusinessActivity(): void {
    setBusinessActivity(undefined);
    businessActivityForm.reset();
    setOpen(true);
  }

  function onSubmitActivity(data: BusinessActivityType): void {
    if (businessActivity && activityIndex !== undefined) {
      update(activityIndex, data);
    } else {
      append(data);
    }

    form.trigger();
    setOpen(false);
  }

  function onSelect(businessActivity: BusinessActivityType, index: number): void {
    setBusinessActivity(businessActivity);
    businessActivityForm.reset(businessActivity, { keepDefaultValues: true });
    setActivityIndex(index);
    setOpen(true);
  }

  function onDelete(): void {
    remove(activityIndex);
    form.trigger();
    setOpen(false);
  }

  const fetcher = useFetcher();

  function onSubmit(data: BusinessActivitiesFormType): void {
    fetcher.submit({ data: JSON.stringify(data) }, {
      method: "post",
    });
  }

  return (
    <>
      <BusinessActivityDialog
        setOpen={setOpen}
        open={open}
        form={businessActivityForm}
        onSubmit={onSubmitActivity}
        businessActivity={businessActivity}
        onDelete={onDelete}
        year={financialYear!}
      />
      <WideContainer>
        <Form {...form}>
          <RemixForm onSubmit={form.handleSubmit(onSubmit, onError)} noValidate id="str-form">
            <FormField
              name="activities"
              control={form.control}
              render={({ fieldState }) => (
                <FormItem>
                  <FormLabel>Business Activities</FormLabel>
                  {fieldState.invalid && <ValidationAlert fieldState={fieldState} />}
                  <FormControl>
                    <BusinessActivitiesList businessActivities={fields} onSelect={onSelect} />
                  </FormControl>
                </FormItem>
              )}
            />
            <div className="flex justify-end pt-5">
              <Button size="sm" onClick={addBusinessActivity} type="button">
                <Plus className="mr-2 size-4 text-white" />
                Add Business Activity
              </Button>
            </div>
          </RemixForm>
        </Form>
      </WideContainer>
    </>
  )
}
