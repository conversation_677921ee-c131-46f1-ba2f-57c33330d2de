import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  DialogHeader,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  RadioGroup,
  RadioGroupItem,
  ScrollArea,
  ScrollBar,
  Textarea,
} from "@netpro/design-system";
import type { UseFormReturn } from "react-hook-form";
import type { JSX } from "react";
import type { AccountingActivityType } from "~/lib/simplified-tax-return/types/corporate-accounting-records/2021/corporate-accounting-records-schema";

type CorporateAccountingActivitiesDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
  onSubmit: (data: AccountingActivityType) => void
  form: UseFormReturn<AccountingActivityType>
};

export function CorporateAccountingActivitiesDialog({
  open,
  setOpen,
  onSubmit,
  form,
}: CorporateAccountingActivitiesDialogProps): JSX.Element {
  return (
    <Dialog open={open} onOpenChange={setOpen} modal>
      <DialogContent
        className="max-w-screen-md"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <ScrollArea className="pr-3">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="p-2" noValidate id="accounting-activity-dialog-form">
              <DialogHeader>
                <p>Intellectual Property Activity</p>
              </DialogHeader>
              <div className="flex-col space-y-2 pt-4">
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field, fieldState }) => (
                    <FormItem className="md:w-1/2 sm:w-full">
                      <FormLabel>Description of assessable income for the calendar year 2021 *</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter description..."
                          invalid={!!fieldState.error}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="relatedPartyIntellectualProperty"
                  render={({ field, fieldState }) => (
                    <FormItem className="w-full">
                      <FormLabel className="flex">
                        Related party intellectual property *
                      </FormLabel>
                      <FormControl>
                        <RadioGroup
                          onValueChange={field.onChange}
                          value={field.value}
                          invalid={!!fieldState.error}
                          orientation="horizontal"
                        >
                          <FormItem className="flex items-center space-x-2 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="true" />
                            </FormControl>
                            <FormLabel className="font-normal">
                              Yes
                            </FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-2 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="false" />
                            </FormControl>
                            <FormLabel className="font-normal">
                              No
                            </FormLabel>
                          </FormItem>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="nonRelatedIntellectualProperty"
                  render={({ field, fieldState }) => (
                    <FormItem className="w-full">
                      <FormLabel className="flex">
                        Non-Related intellectual property *
                      </FormLabel>
                      <FormControl>
                        <RadioGroup
                          onValueChange={field.onChange}
                          invalid={!!fieldState.error}
                          value={field.value}
                          orientation="horizontal"
                        >
                          <FormItem className="flex items-center space-x-2 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="true" />
                            </FormControl>
                            <FormLabel className="font-normal">
                              Yes
                            </FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-2 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="false" />
                            </FormControl>
                            <FormLabel className="font-normal">
                              No
                            </FormLabel>
                          </FormItem>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="nonIntellectualProperty"
                  render={({ field, fieldState }) => (
                    <FormItem className="w-full">
                      <FormLabel className="flex">
                        Non intellectual property *
                      </FormLabel>
                      <FormControl>
                        <RadioGroup
                          onValueChange={field.onChange}
                          invalid={!!fieldState.error}
                          value={field.value}
                          orientation="horizontal"
                        >
                          <FormItem className="flex items-center space-x-2 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="true" />
                            </FormControl>
                            <FormLabel className="font-normal">
                              Yes
                            </FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-2 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="false" />
                            </FormControl>
                            <FormLabel className="font-normal">
                              No
                            </FormLabel>
                          </FormItem>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="incomeYear"
                  render={({ field, fieldState }) => (
                    <FormItem className="md:w-1/2 sm:w-full">
                      <FormLabel>Amount (XCD) of assessable income for the calendar year 2021 *</FormLabel>
                      <FormControl>
                        <Input
                          invalid={!!fieldState.error}
                          type="number"
                          onChange={field.onChange}
                          defaultValue={field.value}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="incomeJanuaryJune"
                  render={({ field, fieldState }) => (
                    <FormItem className="md:w-1/2 sm:w-full">
                      <FormLabel>Amount of assessable income earned Jan - Jun 2021 *</FormLabel>
                      <FormControl>
                        <Input
                          invalid={!!fieldState.error}
                          type="number"
                          onChange={field.onChange}
                          defaultValue={field.value}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <DialogFooter className="pt-4">
                <div className="flex w-full justify-end">
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline" onClick={() => setOpen(false)} type="button">Cancel</Button>
                    <Button size="sm" variant="default" type="submit" form="accounting-activity-dialog-form">Save Accounting Activity</Button>
                  </div>
                </div>
              </DialogFooter>
            </form>
          </Form>
          <ScrollBar />
        </ScrollArea>
      </DialogContent>
    </Dialog>
  )
}
