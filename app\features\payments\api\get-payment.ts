import { client } from "~/lib/api-client";
import type { ClientRequestHeaders } from "~/lib/types/client-request-headers";
import type { PaymentStatusType } from "~/lib/payments/utilities/payment-status";

export type PaymentTransaction = {
  result: string
  resultCode: string
  resultMessage: string
  transactionId: string
  status: string
  processCreatedAt: string
  paidAt: string
  isFinished: boolean
  paymentProviderId: string
}

export type Payment = {
  id: string
  legalEntityId: string
  currencyId: string
  amount: number
  status: PaymentStatusType
  paymentTransactions: PaymentTransaction[]
}

export function getPayment(
  { accessToken, userId, paymentId }: { paymentId: string } & ClientRequestHeaders,
): Promise<Payment> {
  return client.get<Payment>(
    `/client/payments/${paymentId}`,
    accessToken,
    userId,
  );
}
