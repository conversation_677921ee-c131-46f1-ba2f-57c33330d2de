import { Button, Checkbox, Dialog, DialogContent, DialogFooter, Label, notify } from "@netpro/design-system";
import { useBeforeUnload, useFetcher, useRouteLoaderData } from "@remix-run/react";
import { type JSX, useCallback, useEffect, useId, useState } from "react";
import { Repeat as IconRepeat } from "lucide-react";
import type { Company } from "../api/get-companies";
import CompaniesSearch from "./CompaniesSearch";
import type { CompanyDTO, RootLoaderData } from "~/lib/auth/types/session-type";

type CompaniesSearchDialogProps = {
  open: boolean
  setOpen: (isOpen: boolean) => void
  onChangeMasterClient?: () => void
  onChange?: (selectedCompany: CompanyDTO) => void
}

export default function CompaniesSearchDialog({
  open,
  setOpen,
  onChangeMasterClient,
  onChange,
}: CompaniesSearchDialogProps): JSX.Element | null {
  const [companies, setCompanies] = useState<Company[] | null>(null);
  const [includeInactiveCompanies, setIncludeInactiveCompanies] = useState<boolean>(false);
  const rootLoader = useRouteLoaderData<RootLoaderData>("root");
  const checkboxId = useId();
  const fetcher = useFetcher<any>();
  const cancelMasterClientFetcher = useFetcher<any>();
  const handleSelectCompany = (selectedCompany: Company): void => {
    onChange?.(selectedCompany);
    fetcher.submit(
      {
        id: selectedCompany.companyId,
        isActive: selectedCompany.isActive,
      },
      { method: "post", action: "/api/companies" },
    );
    setOpen(false);
  }
  const handleSearchCompany = (searchTerm: string): void => {
    fetcher.load(`/api/companies?search=${encodeURIComponent(searchTerm)}&inactive=${encodeURIComponent(includeInactiveCompanies)}`);
  }
  const handleOpenChange = (isOpen: boolean): void => {
    setOpen(isOpen);

    if (rootLoader?.sessionData.queuedMasterClientUpdate) {
      notify({
        variant: "warning",
        title: "Master Client change cancelled",
        message: "Your Master Client change was cancelled because you did not select a company.",
      });
      cancelMasterClientFetcher.submit(
        {},
        { method: "post", action: "/api/master-clients/cancel-change" },
      );
    }
  };

  useBeforeUnload(
    useCallback(() => {
      if (rootLoader?.sessionData.queuedMasterClientUpdate) {
        cancelMasterClientFetcher.submit(
          {},
          { method: "post", action: "/api/master-clients/cancel-change" },
        );
      }
    }, [cancelMasterClientFetcher, rootLoader?.sessionData.queuedMasterClientUpdate]),
  );

  function handleChangeMasterClient(): void {
    setOpen(false);
    onChangeMasterClient?.();
  }

  useEffect(() => {
    if (fetcher.data) {
      setCompanies(fetcher.data?.companies || []);
    }
  }, [fetcher.data]);

  useEffect(() => {
    if (open) {
      if (companies === null && fetcher.state === "idle") {
        fetcher.load("/api/companies");
      }
    } else {
      setCompanies(null);
      setIncludeInactiveCompanies(false);
    }
  }, [companies, fetcher, open]);

  if (!open) {
    return null;
  }

  return (
    <Dialog
      modal
      open={open}
      onOpenChange={(isOpen) => {
        handleOpenChange(isOpen);
      }}
    >
      <DialogContent
        className="max-w-screen-md"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <div>
          <CompaniesSearch
            initialValues={companies || []}
            isSearching={fetcher.state === "loading"}
            onSelect={handleSelectCompany}
            onSearchCompany={handleSearchCompany}
          />
        </div>
        <DialogFooter>
          <div className="flex items-center flex-grow justify-between">
            <div className="flex items-center gap-2">
              <Checkbox
                id={checkboxId}
                onCheckedChange={() => setIncludeInactiveCompanies(!includeInactiveCompanies)}
              />
              <Label
                className="peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                htmlFor={checkboxId}
              >
                Include inactive companies
              </Label>
            </div>

            {onChangeMasterClient && (
              <Button
                type="button"
                variant="outline"
                size="sm"
                className="gap-1.5"
                onClick={handleChangeMasterClient}
              >
                <IconRepeat size={16} />
                Change Master Client
              </Button>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
