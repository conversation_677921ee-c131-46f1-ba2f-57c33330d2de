.link-underline-animation,
	.sub-link {
  position: relative;
  display: inline-block;
}

.link-underline-animation::after {
  position: absolute;
  left: 0px;
  bottom: 0px;
  height: 0.25rem;
  width: 100%;
  transform-origin: bottom left;
  --tw-scale-x: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  border-width: 0px;
  content: '';
  transition: transform 0.25s ease-out;
}

.link-underline-animation:hover::after {
  left: 0px;
  bottom: 0.25rem;
  transform-origin: bottom right;
  --tw-scale-x: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}