import { Badge, cn } from "@netpro/design-system";
import { <PERSON> } from "@remix-run/react";
import type { MenuItem } from "./MenuItem";

export default function ParentMenuItem({ item, active, ...props }: {
  item: Omit<MenuItem, "icon" | "children">
  active: boolean
}): JSX.Element {
  return (
    <li {...props}>
      <Link
        to={item.href}
        className={cn(
          active ? "bg-gray-100" : "hover:bg-gray-100",
          "flex w-full justify-between rounded-md gap-3 py-2 pl-12 pr-2.5 text-sm leading-5 font-inter text-gray-700",
        )}
      >
        <span className="block overflow-hidden text-ellipsis whitespace-nowrap pr-10" title={item.label}>
          {item.label}
        </span>

        {item.count && (
          <Badge
            label={item.count.toString()}
            variant="info"
            className="h-auto text-xs/5 font-semibold font-inter rounded-2xl py-px px-3"
          />
        )}
      </Link>
    </li>
  );
}
