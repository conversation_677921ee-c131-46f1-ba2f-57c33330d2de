import type { ReactNode } from "react";
import { useMemo } from "react";
import type { Company } from "../api/get-companies"
import SearchableList from "~/components/searchable-list/SearchableList";
import type { SearchResultsItemDTO } from "~/lib/types/search-results-type";
import { useDelayedState } from "~/lib/utilities/use-delayed-state";

function mapCompaniesToResultItems(companies: Company[]): SearchResultsItemDTO[] {
  return companies.map((company: Company) => ({
    id: company.companyId,
    title: company.companyName,
    subtitle: company.incorporationNumber
      ? `Incorporation number (${company.incorporationNumber}) | ${company.jurisdictionName}`
      : `${company.jurisdictionName}`,
    isActive: company.isActive,
    isClosing: company.vpEntityStatus === "Closing",
  }));
}

type CompaniesSearchProps = {
  initialValues: Company[]
  isSearching: boolean
  onSelect: (company: Company) => void
  onSearchCompany: (search: string) => void
}

export default function CompaniesSearch({
  initialValues,
  isSearching,
  onSelect,
  onSearchCompany,
}: CompaniesSearchProps): ReactNode {
  const handleSelect = (selectedCompanyId: string): void => {
    const selectedCompany = initialValues.find(company => company.companyId === selectedCompanyId);

    if (selectedCompany) {
      onSelect(selectedCompany)
    }
  }
  const memoizedItems = useMemo(() => mapCompaniesToResultItems(initialValues), [initialValues]);
  const delayedState = useDelayedState(isSearching, 800);

  return (
    <div className="flew w-full pb-2">
      <SearchableList
        title="Choose a Company"
        placeholder="Search by company name or incorporation number"
        onSelect={handleSelect}
        onSearch={onSearchCompany}
        isSearching={delayedState}
        items={memoizedItems}
      />
    </div>
  )
}
