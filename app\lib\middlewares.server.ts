import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import terms from "./middleware/terms";
import requireBfrModule from "./middleware/requireBfrModule";
import mfa from "./middleware/mfa";
import auth from "~/lib/middleware/auth";
import redirectMcc from "~/lib/middleware/redirectMcc";
import requireMcc from "~/lib/middleware/requireMcc";
import requireCompany from "~/lib/middleware/requireCompany";
import redirectCompany from "~/lib/middleware/redirectCompany";
import requireStrModule from "~/lib/middleware/requireStrModule";
import requireBoDirModule from "~/lib/middleware/requireBoDirModule";

/**
 * Middleware functions are functions that run at the beginning of a loader or action function.
 * They can be used to perform common tasks like authentication, authorization, and more.
 * In the basis, a middleware function will throw a Response to intercept the request and respond early.
 * Optionally, a middleware function can return data that can be used in the loader or action function.
 *
 * Conventions:
 * - Create a new file for each middleware function.
 * - Name the file with the middleware key name (e.g. auth.ts, requireMcc.ts).
 * - Export the middleware function as a default export.
 * - Add the middleware function to the `middlewareFunctions` object below.
 *
 * Type inference is used to ensure that the middleware functions are correctly typed.
 *
 * Note: if the name of the middleware function is the same as its desired key, you can use the shorthand syntax:
 * ```ts
 * const middlewareFunctions = {
 *  auth,
 *  redirectMcc,
 *  ...
 * };
 *
 * vs
 *
 * const middlewareFunctions = {
 *   auth: auth,
 *   redirectMcc: redirectMcc,
 *   ...
 * };
 */
const middlewareFunctions = {
  auth,
  terms,
  mfa,
  redirectMcc,
  requireMcc,
  requireCompany,
  redirectCompany,
  requireStrModule,
  requireBoDirModule,
  requireBfrModule,
};

export type MiddlewareProps = {
  request: ActionFunctionArgs["request"] | LoaderFunctionArgs["request"]
}

export type MiddlewareResponse<T = void> = Promise<T>;

// Helper Types
type MiddlewareFunctions = typeof middlewareFunctions;

type MiddlewareResult<K extends keyof MiddlewareFunctions> =
  K extends keyof MiddlewareFunctions
    ? Awaited<ReturnType<MiddlewareFunctions[K]>>
    : never;

type UnionToIntersection<U> = (
  U extends any ? (k: U) => void : never
) extends (k: infer I) => void
  ? I
  : never;

type Simplify<T> = { [KeyType in keyof T]: T[KeyType] };

// The `middlewares` function
export async function middleware<
  T extends Array<keyof MiddlewareFunctions>,
>(
  middlewareNames: T,
  request: Request,
): Promise<
    Simplify<
      UnionToIntersection<
        MiddlewareResult<T[number]>
      >
    >
  > {
  const results: Array<Record<string, any> | void> = [];

  for (const name of middlewareNames) {
    const result = await middlewareFunctions[name]({ request });
    results.push(result);
  }

  return Object.assign({}, ...results);
}
