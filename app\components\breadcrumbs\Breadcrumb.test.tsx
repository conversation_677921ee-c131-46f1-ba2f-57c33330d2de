import { describe, expect, it } from "vitest";
import { render } from "@testing-library/react";
import { createRemixStub } from "@remix-run/testing";
import type { JSX } from "react";
import type { BreadCrumbProps } from "./Breadcrumb";
import { Breadcrumb } from "./Breadcrumb";

describe("breadcrumb", () => {
  const mockData: BreadCrumbProps["data"] = [
    { href: "/home", name: "Home" },
    { href: "/products", name: "Products" },
    { href: "/products/electronics", name: "Electronics" },
  ];
  const RemixStub = createRemixStub([
    {
      path: "/",
      Component: (): JSX.Element => {
        return <Breadcrumb data={mockData} />;
      },
    },
  ]);

  it("renders the breadcrumb items", () => {
    const { container } = render(<RemixStub />);
    expect(container).toBeInTheDocument();
  });

  it("should render three items", () => {
    const { container } = render(<RemixStub />);
    expect(container.querySelectorAll("li")).toHaveLength((mockData.length * 2) - 1);
  });

  it("should render links", () => {
    const { container } = render(<RemixStub />);
    const links = container.querySelectorAll("a");
    expect(links).toHaveLength(mockData.length - 1);
  });

  // the ol should only contain li children
  it("should only contain li children", () => {
    const { container } = render(<RemixStub />);
    const ol = container.querySelector("ol");
    const children = ol?.children;
    if (children) {
      for (let i = 0; i < children.length; i++) {
        expect(children[i].tagName).toBe("LI");
      }
    }
  });
});
