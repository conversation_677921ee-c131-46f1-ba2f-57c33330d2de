import { expect, test } from "@playwright/test";

const baseURL = process.env.URL || "http://localhost:3000";

test.describe("Landing Page", () => {
  test("display landing page by <PERSON>", async ({ page }) => {
    await page.goto(baseURL);
    await expect(
      await page.locator("h1", { hasText: "Landing Page by <PERSON>" }),
    ).toBeVisible();
  });

  test("can navigate to the login page", async ({ page }) => {
    await page.goto(baseURL);
    await page.locator("a[href='/login']").click();
    // eslint-disable-next-line unicorn/prefer-dom-node-text-content
    const title = await page.locator("#loginHeaderDescription").innerText();

    expect(page.url()).toContain("ciamlogin.com");
    expect(title).toBe("Sign in to access the Private Client Portal");
  });
});
