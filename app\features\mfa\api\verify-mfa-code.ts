import { z } from "zod";
import { client } from "~/lib/api-client";
import type { ClientRequestHeaders } from "~/lib/types/client-request-headers";

export const mfaCodeSchema = z.object({
  code: z.string({
    required_error: "Authentication code is required.",
  }).trim().length(6, { message: "Authentication code must be 6 characters long." }),
});

export type mfaCodeType = z.infer<typeof mfaCodeSchema>;

export type VerificationResult = {
  verificationCode: string
  success: boolean
}

export function verifyMfaCode({ data, accessToken, userId }: { data: mfaCodeType } & ClientRequestHeaders): Promise<VerificationResult> {
  return client.post(`/security/mfa/users/${userId}/verification`, accessToken, userId, {}, {
    params: data,
  });
}
