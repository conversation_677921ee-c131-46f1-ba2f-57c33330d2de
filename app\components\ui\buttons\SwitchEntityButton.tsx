import type { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ReactNode } from "react";
import { Building2, Pyramid, Repeat } from "lucide-react";

type SwitchEntityButtonProps = {
  children: ReactNode
  variant?: "pyramid" | "building"
  onClick?: ReactEventHandler
}

export function SwitchEntityButton({ variant = "building", onClick, children }: SwitchEntityButtonProps): JSX.Element {
  return (
    <button type="button" onClick={onClick} className="active:translate-y-0.5 relative flex items-center cursor-pointer w-full h-10 rounded-sm py-2 px-2 gap-3 bg-blue-600 hover:bg-blue-400 transition-colors duration-150 group">
      {variant === "pyramid" && <PyramidIcon />}
      {variant === "building" && <BuildingIcon />}
      <div className="flex flex-col gap-0 w-full">
        {children}
      </div>
      {onClick && (
        <Repeat
          size={16}
          className="text-blue-300 group-hover:text-white absolute right-2"
        />
      )}
    </button>
  );
}

function PyramidIcon(): JSX.Element {
  return (
    <div className="absolute w-auto h-auto top-0 left-0 p-0.5 gap-2.5 opacity-20 -rotate-12">
      <Pyramid
        size={40}
        strokeWidth={1}
        className="text-blue-300"
      />
    </div>
  );
}

function BuildingIcon(): JSX.Element {
  return (
    <div className="absolute w-auto h-auto opacity-20 -rotate-3">
      <Building2
        size={35}
        strokeWidth={1}
        className="text-blue-200"
      />
    </div>
  );
}
