import type { ActionFunctionArgs, LoaderFunctionArgs, TypedResponse } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import type { ReactNode } from "react";
import { Breadcrumb } from "~/components/breadcrumbs/Breadcrumb";
import { requireValidPage, useCurrentStep } from "~/lib/basic-financial-report/hooks/use-form-steps";
import { getFormAction } from "~/lib/basic-financial-report/utilities/form-action.server";
import { getFormLoader } from "~/lib/basic-financial-report/utilities/form-loader.server";
import type { PageSlug } from "~/lib/basic-financial-report/utilities/form-pages";
import type { DocumentDTO, SubmissionKeyValueDTO } from "~/services/api-generated";

const title = "Submission for" as const;
const breadCrumbList = [
  {
    href: "/",
    name: "Basic Financial Report",
  },
];

export const handle = {
  breadcrumb: (): ReactNode => <Breadcrumb data={breadCrumbList} />,
  title,
};

export async function action(actionArgs: ActionFunctionArgs): Promise<TypedResponse<null> | undefined> {
  const { page } = requireValidPage(actionArgs.params);

  return getFormAction(actionArgs, page);
}

export type BasicFinancialReportData = {
  submission: SubmissionKeyValueDTO
  page: PageSlug
  mappedDocuments: Record<string, DocumentDTO>
}

export type BasicFinancialReportContainerLoader = TypedResponse<never> | BasicFinancialReportData
export async function loader(loaderArgs: LoaderFunctionArgs): Promise<BasicFinancialReportContainerLoader> {
  const { page } = requireValidPage(loaderArgs.params);

  return getFormLoader(loaderArgs, page);
}

export default function BasicFinancialReportContainer(): ReactNode | never {
  const { page } = useLoaderData<typeof loader>();
  const currentStep = useCurrentStep(page);

  if (!currentStep) {
    throw new Error("Current step is not available");
  }

  return currentStep.component();
}
