import type { ReactNode } from "react";
import { useMemo } from "react";
import type { z } from "zod";
import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import type { PageSlug } from "../utilities/form-pages";
import { Pages } from "../utilities/form-pages";
import { formSteps } from "../utilities/form-steps";

export type FormStep = {
  name: string
  page: PageSlug
  component: (args?: any) => ReactNode
  validationSchema: z.ZodEffects<any, any> | z.ZodObject<any, any>
  previousPage: string | ((submission: Record<string, any>) => string | null) | null
  nextPage: string | ((submission: Record<string, any>) => string | null) | null
};

// Use in components/client side to benefit from React hooks
export function useFormSteps(): FormStep[] {
  return formSteps;
}

export function getFirstStep(): FormStep {
  return formSteps[0];
}

export function useCurrentStep(page: string): FormStep | undefined {
  return useMemo(() => getCurrentStep(page), [page]);
}

export function useNextStep(submission: Record<string, any>, page: string): string | null {
  return useMemo(() => getNextStep(submission, page), [submission, page]);
}

export function usePreviousStep(submission: Record<string, any>, page: string): string | null {
  return useMemo(() => getPreviousStep(submission, page), [submission, page]);
}

// Use in server side (loaders, actions)
export function getCurrentStep(page: string): FormStep | undefined {
  return formSteps.find(step => step.page === page);
}

export function getNextStep(submission: Record<string, any>, page: string): string | null {
  const current = formSteps.find(step => step.page === page);
  if (!current) {
    return null;
  }

  if (typeof current.nextPage === "function") {
    return current.nextPage(submission);
  } else {
    return current.nextPage;
  }
}

export function getPreviousStep(submission: Record<string, any>, page: string): string | null {
  const current = formSteps.find(step => step.page === page);
  if (!current) {
    return null;
  }

  if (typeof current.previousPage === "function") {
    return current.previousPage(submission);
  } else {
    return current.previousPage;
  }
}

export function requireValidPage(params: ActionFunctionArgs["params"] | LoaderFunctionArgs["params"]): {
  page: PageSlug
} {
  // Compare pageName param against Pages values to validate the page name
  const page = params?.pageName as PageSlug;
  const validPages = Object.values(Pages);
  if (!validPages.includes(page)) {
    throw new Response("Requested page does not exist.", { status: 404 });
  }

  return { page };
}
