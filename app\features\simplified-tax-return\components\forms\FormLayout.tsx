import { <PERSON><PERSON>, Separator } from "@netpro/design-system";
import { useLocation, useNavigate } from "@remix-run/react";
import { Check, ChevronRight } from "lucide-react";
import type { JSX, ReactNode } from "react";
import { ProgressBar } from "~/components/forms/ProgressBar";
import { usePreviousStep } from "~/lib/simplified-tax-return/hooks/use-form-steps";
import { Pages } from "~/lib/simplified-tax-return/utilities/form-pages";
import { useSubmission } from "~/lib/submission/context/use-submission";

type FormLayoutProps = {
  children: ReactNode
  formSteps: {
    name: string
    page: string
  }[]
};

export function FormLayout({ formSteps, children }: FormLayoutProps): JSX.Element {
  const location = useLocation();
  const navigate = useNavigate();
  const { submissionData, financialYear, canContinue } = useSubmission();
  const currentLocation = location.pathname;
  const previousPage = usePreviousStep(submissionData, financialYear!, currentLocation.split("/").pop()!)
  const stepPages = formSteps.map(step => step.page);
  const formStepIndex = stepPages.indexOf(currentLocation.split("/").pop()!);

  function handleBack(): void {
    if (!previousPage) {
      return;
    }

    navigate(currentLocation.replace(stepPages[formStepIndex], previousPage));
  }

  return (
    <div className="flex flex-col w-full justify-between">
      <div className="px-4 pt-1 pb-5">
        {formStepIndex >= 0 && <ProgressBar items={formSteps.map(step => step.name)} activeIndex={formStepIndex} />}
        {children}
      </div>
      <div>
        <Separator orientation="horizontal" />
        <div className="flex justify-between mt-2">
          <span className="text-xs text-gray-500 my-auto">A draft will automatically be saved</span>
          <div className="flex space-x-2">
            {formStepIndex > 0 && (
              <Button variant="outline" onClick={handleBack}>
                Back
              </Button>
            )}
            {currentLocation.endsWith(Pages.FINALIZE)
              ? (
                  <Button type="submit" form="str-form" disabled={!canContinue}>
                    Finalize submission
                    <Check className="ml-2 size-4 text-white" />
                  </Button>
                )
              : (
                  <Button type="submit" form="str-form" disabled={!canContinue}>
                    Next
                    <ChevronRight className="ml-2 size-4 text-white" />
                  </Button>
                )}
          </div>
        </div>
      </div>
    </div>
  )
}
