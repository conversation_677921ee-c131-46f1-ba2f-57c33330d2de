# This is the local environment file. The variables in this file can be shared with the developers internally.
# Do not use production-level secrets in this file.
API_BASE_URL=https://localhost:7108
APPLICATION_BASE_URL=http://localhost:3000

# When proxying with <PERSON>, use this URL
#API_BASE_URL=https://localhost:62407

# Entra ID External ID Configuration
ENTRA_EXTERNAL_ID_CLIENT_ID=6bc0691d-4c10-499f-a56a-a7b36a4093b9
ENTRA_EXTERNAL_ID_TENANT_ID=4053da93-8216-46fd-a82a-a32155693958
ENTRA_EXTERNAL_ID_CLIENT_SECRET=****************************************
ENTRA_EXTERNAL_ID_TENANT_SUBDOMAIN=netprogroupnvexternalidtest
ENTRA_EXTERNAL_ID_REDIRECT_URI=http://localhost:3000/auth/callback

# Entra Application Configuration
APPLICATION_CLIENT_ID=6071e39c-3f4d-4f25-960a-d02680f759ea
APPLICATION_TENANT_ID=30350f35-feb0-4bbc-877f-43ff406a41e5
APPLICATION_CLIENT_SECRET=****************************************
ENTRA_API_SCOPE=api://bdcf01f6-a196-478c-b778-7cbf57f19e5f/.default

# Session secret can be a long random string
SESSION_SECRET=McRAjFYrbdvLw6Xm3hYmZa4CNJtyhAJhtiudbDEFbXfHq8tuiQFJytEj9vnVGhmwTdzosv

NODE_TLS_REJECT_UNAUTHORIZED=0
NODE_DEBUG=net,stream
