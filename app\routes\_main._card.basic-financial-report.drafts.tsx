import type { LoaderFunctionArgs, TypedResponse } from "@remix-run/node";
import { json } from "@remix-run/node";
import { Link, useLoaderData } from "@remix-run/react";
import type { JSX } from "react";
import { Button } from "@netpro/design-system";
import { ChevronRight } from "lucide-react";
import { Breadcrumb } from "~/components/breadcrumbs/Breadcrumb";
import { Modules } from "~/lib/utilities/modules";
import { requireActiveModule } from "~/features/modules/api/get-modules";
import { SubmissionStatusNames } from "~/lib/submission/utilities/submission-status";
import { middleware } from "~/lib/middlewares.server";
import { Pages } from "~/lib/basic-financial-report/utilities/form-pages";
import { SubmissionRow } from "~/components/basic-financial-report/SubmissionRow";
import type { SubmissionDTO } from "~/services/api-generated";
import { clientGetCompanyModuleSubmissions } from "~/services/api-generated";
import { CenteredMessage } from "~/components/errors/CenteredMessage";
import { authHeaders } from "~/lib/auth/utils/auth-headers";

const title = "Drafts for" as const;
const breadCrumbList = [
  {
    href: "/basic-financial-report/new",
    name: "Basic Financial Report",
  },
];

type LoaderResponse = {
  submissions: SubmissionDTO[]
}

export const handle = {
  breadcrumb: (): JSX.Element => <Breadcrumb data={breadCrumbList} />,
  title,
};

export async function loader({ request }: LoaderFunctionArgs): Promise<TypedResponse<LoaderResponse | never>> {
  const { company } = await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany", "requireCompany"], request);
  const { module } = await requireActiveModule({ request, key: Modules.BASIC_FINANCIAL_REPORT, companyId: company.companyId });
  const { data: submissionData, error } = await clientGetCompanyModuleSubmissions({
    headers: await authHeaders(request),
    path: {
      companyId: company.companyId,
      moduleId: module.id as string,
    },
  })

  if (error) {
    throw new Response(error.exceptionMessage as string, { status: 500 })
  }

  if (!submissionData?.data) {
    throw new Response("Submissions not found", { status: 404 })
  }

  const submissions = submissionData?.data?.filter(
    submission => submission.statusText === SubmissionStatusNames.Draft || submission.statusText === SubmissionStatusNames.Revision,
  );

  return json({ submissions });
}

export default function BFRDraftSubmissions(): JSX.Element {
  const { submissions } = useLoaderData<typeof loader>();

  return (
    <div className="flex flex-col w-full justify-between">
      <div className="px-4 py-2.5">
        {submissions && submissions.length > 0
          ? (
              submissions.map(submission => (
                <SubmissionRow
                  submission={submission}
                  key={submission.id}
                  deleteDraftAction
                  moduleUrl="basic-financial-report"
                  continuePageName={Pages.FINANCIAL_PERIOD}
                />
              ))
            )
          : (
              <CenteredMessage title="No draft submissions">
                <Button asChild variant="outline">
                  <Link to="/basic-financial-report/submissions">
                    View submissions
                    <ChevronRight className="w-6" />
                  </Link>
                </Button>
                <span className="font-semibold">or</span>
                <Button asChild variant="default" className="inline-flex gap-1">
                  <Link to="/basic-financial-report/new">
                    File new submission
                    <ChevronRight className="w-6" />
                  </Link>
                </Button>
              </CenteredMessage>
            )}
      </div>
    </div>
  );
}
