import { Alert } from "@netpro/design-system";
import type { ControllerFieldState } from "react-hook-form";

export function ValidationAlert({ fieldState }: { fieldState: ControllerFieldState }): JSX.Element {
  return (
    <div className="py-4">
      <Alert title="Validation failed" variant="warning">
        <ul className="list-disc">
          {fieldState?.error && Array.isArray(fieldState.error) && fieldState.error.map((error) => {
            return error ? <li key={error.message}>{error.message}</li> : null;
          })}
        </ul>
      </Alert>
    </div>
  )
}
