import { type ActionFunctionArgs, type TypedResponse, json, redirect } from "@remix-run/node";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { commitSession, getSession } from "~/lib/auth/utils/session.server";
import { getCurrentStep } from "~/lib/basic-financial-report/hooks/use-form-steps";
import { mapDocumentIdsToKeys } from "~/lib/basic-financial-report/utilities/documents";
import { Pages } from "~/lib/basic-financial-report/utilities/form-pages";
import { middleware } from "~/lib/middlewares.server";
import { getFlattenedSubmission } from "~/lib/submission/utilities/submission-data-set";
import { getUnflattenedDataSet } from "~/lib/submission/utilities/submission-data-set-auto";
import { clientGetSubmission, clientPutSubmissionDataSet } from "~/services/api-generated";

export async function action({ request, params }: ActionFunctionArgs): Promise<TypedResponse<null> | undefined> {
  await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany", "requireBfrModule"], request);
  const session = await getSession(request.headers.get("Cookie"));
  const { id, documentId, pageName } = params;

  if (!id) {
    throw new Error("Submission ID is required");
  }

  if (!documentId) {
    throw new Error("Document ID is required");
  }

  if (!pageName) {
    throw new Error("Page Name is required");
  }

  const formData = await request.formData();
  const data = JSON.parse(formData.get("data") as string) as Record<string, string>;
  const location = formData.get("location") as string || "/"
  const { data: submission } = await clientGetSubmission({
    headers: await authHeaders(request),
    path: { submissionId: id },
    query: { includeFormDocument: true },
  });

  if (!submission) {
    throw new Error("Submission not found")
  }

  const currentStep = getCurrentStep(pageName);
  if (!currentStep) {
    throw new Error("Current step is not available");
  }

  const unflattenedData = getUnflattenedDataSet(submission);
  // Update the submission dataset with the new data but keep the other pages intact
  const unflattenedNewSubmission = {
    ...unflattenedData,
    [pageName]: { ...unflattenedData[pageName], ...data },
  };
  const newSubmissionData = getFlattenedSubmission(unflattenedNewSubmission);
  const oldDocumentsId = submission.documentIds ?? [];
  // Combine existing document IDs with the new document ID, ensuring uniqueness
  const documentIds = Array.from(new Set([...oldDocumentsId, documentId]));
  let filteredDocumentIds: string[] = documentIds;
  if (submission.documentIds?.length) {
    // Map document IDs to their corresponding keys in the financial period step, if applicable
    const financialPeriodDocuments = unflattenedNewSubmission[Pages.FINANCIAL_PERIOD]
      ? mapDocumentIdsToKeys(unflattenedNewSubmission[Pages.FINANCIAL_PERIOD], documentIds)
      : {};
    // Map document IDs to their corresponding keys in the financial reports details step, if applicable
    const financialReportsDetailsDocuments = unflattenedData[Pages.FINANCIAL_REPORTS_DETAILS]
      ? mapDocumentIdsToKeys(unflattenedData[Pages.FINANCIAL_REPORTS_DETAILS], submission.documentIds)
      : {};
    // Combine valid document IDs from both steps into a single set to ensure uniqueness
    const validDocumentIds = new Set([
      ...Object.keys(financialPeriodDocuments),
      ...Object.keys(financialReportsDetailsDocuments),
    ]);

    // Filter the original document IDs to include only those present in the valid document IDs set
    filteredDocumentIds = validDocumentIds.size ? documentIds.filter(id => validDocumentIds.has(id)) : documentIds;
    // This step ensures that only the document IDs linked to the current form steps are preserved.
  }

  try {
    const { error } = await clientPutSubmissionDataSet({ headers: await authHeaders(request), path: { submissionId: id }, body: { id, dataSet: newSubmissionData, documentIds: filteredDocumentIds } })

    if (error) {
      session.flash("notification", { title: "Error!", message: error.exceptionMessage, variant: "error" });

      return redirect(location, {
        headers: { "Set-Cookie": await commitSession(session) },
      });
    }

    return json(null, { status: 200, headers: { "Set-Cookie": await commitSession(session) } });
  } catch (error) {
    if (error instanceof Response) {
      const errorMessage = await error.text();

      if (errorMessage) {
        session.flash("notification", { title: "An Error has occurred", message: errorMessage, variant: "error" });

        return json(null, { status: error.status, headers: { "Set-Cookie": await commitSession(session) } });
      }
    } else {
      throw error;
    }
  }
}
