import { z } from "zod";
import { nonEmptyString, nonNullDate, stringBoolean } from "~/lib/utilities/zod-validators";

export const finalizeSchema = z.object({
  confirmationTrueInformation: stringBoolean(),
  confirmationUnderstand: stringBoolean(),
  confirmationAwarePerjury: stringBoolean(),
  dateOfSignature: nonNullDate("Date of signature"),
  addressOfPersonDeclaring: nonEmptyString("Address of person making the declaration"),
  addressOfPersonDeclaring2: z.string().optional(),
  zipCode: nonEmptyString("Zip code"),
  city: nonEmptyString("City"),
  country: nonEmptyString("Country"),
  nameOfPersonDeclaring: nonEmptyString("Name of person stating the declaration"),
  returnMadeAs: z.enum(["onMyOwnBehalf", "asOfficer", "asAttorney", "asTrustee"], {
    required_error: "Please select one of the options.",
  }),
  onMyOwnBehalf: z.string().optional(),
  asOfficer: z.string().optional(),
  asAttorney: z.string().optional(),
  asTrustee: z.string().optional(),
})
  .refine(data => !(data.confirmationTrueInformation !== "true"), {
    message: "Required.",
    path: ["confirmationTrueInformation"],
  })
  .refine(data => !(data.confirmationUnderstand !== "true"), {
    message: "Required.",
    path: ["confirmationUnderstand"],
  })
  .refine(data => !(data.confirmationAwarePerjury !== "true"), {
    message: "Required.",
    path: ["confirmationAwarePerjury"],
  })
  .refine(data => !(data.returnMadeAs === "onMyOwnBehalf" && !data.onMyOwnBehalf), {
    message: "Name is required.",
    path: ["onMyOwnBehalf"],
  })
  .refine(data => !(data.returnMadeAs === "asOfficer" && !data.asOfficer), {
    message: "Name is required.",
    path: ["asOfficer"],
  })
  .refine(data => !(data.returnMadeAs === "asAttorney" && !data.asAttorney), {
    message: "Name is required.",
    path: ["asAttorney"],
  })
  .refine(data => !(data.returnMadeAs === "asTrustee" && !data.asTrustee), {
    message: "Name is required.",
    path: ["asTrustee"],
  });

export type FinalizeType = z.infer<typeof finalizeSchema>;
