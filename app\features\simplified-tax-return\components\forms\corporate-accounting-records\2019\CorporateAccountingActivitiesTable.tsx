import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@netpro/design-system";
import { Pencil, X } from "lucide-react";
import type { JSX } from "react";
import type { AccountingActivityType } from "~/lib/simplified-tax-return/types/corporate-accounting-records/2019-2020/corporate-accounting-records-schema";

type CorporateAccountingActivitiesTableProps = {
  activities: (AccountingActivityType & { formArrayId: string })[]
  onSelect: (activity: AccountingActivityType, index: number) => void
  onDelete: (index: number) => void
}

export function CorporateAccountingActivitiesTable({
  activities,
  onSelect,
  onDelete,
}: CorporateAccountingActivitiesTableProps): JSX.Element {
  const formatYesNoBoolean = (input: string): string => {
    return input === "true" ? "Yes" : "No"
  }

  return (
    <div className="border-gray-200 border mt-4">
      <ScrollArea>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Description of assessable income covered by grandfathering</TableHead>
              <TableHead>Related party intellectual property</TableHead>
              <TableHead>Non-Related intellectual property</TableHead>
              <TableHead>Non intellectual property</TableHead>
              <TableHead>Amount of assessable income covered by grandfathering</TableHead>
              <TableHead></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {!activities.length && (
              <TableRow>
                <TableCell colSpan={5} className="text-center text-gray-500">
                  No activities available
                </TableCell>
              </TableRow>
            )}
            {activities.length > 0 && activities.map((activity, index) => (
              <TableRow key={activity.formArrayId}>
                <TableCell>{activity.description}</TableCell>
                <TableCell>
                  {formatYesNoBoolean(activity.relatedPartyIntellectualProperty)}
                </TableCell>
                <TableCell>
                  {formatYesNoBoolean(activity.nonRelatedIntellectualProperty)}
                </TableCell>
                <TableCell>
                  {formatYesNoBoolean(activity.nonIntellectualProperty)}
                </TableCell>
                <TableCell>{`$ ${activity.income}`}</TableCell>
                <TableCell className="flex justify-end gap-2">
                  <Button type="button" size="sm" variant="secondary" onClick={() => onSelect(activity, index)}>
                    <Pencil className="mr-2 size-4" />
                    Edit
                  </Button>
                  <Button type="button" size="sm" variant="destructive" onClick={() => onDelete(index)}>
                    <X className="mr-2 size-4" />
                    Delete
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>
    </div>
  )
}
