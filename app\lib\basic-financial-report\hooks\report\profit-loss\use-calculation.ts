import type { CurrentAssetsDetailsSchemaType } from "../../../types/current-assets-details-schema";
import type { EquityDetailsSchemaType } from "../../../types/equity-details-schema";
import type { ExpenseDetailsSchemaType } from "../../../types/expense-details-schema";
import type { IncomeDetailsSchemaType } from "../../../types/income-details-schema";
import type { LiabilitiesDetailsSchemaType } from "../../../types/liabilities-details-schema";
import type { NonCurrentAssetsDetailsSchemaType } from "../../../types/non-current-assets-details-schema";
import { Pages } from "../../../utilities/form-pages";
import { sumArray, toNumber } from "~/lib/basic-financial-report/utilities/calculation";

export function useProfitLossCalculation(submissionData: Record<string, any>) {
  const { securitiesValue } = submissionData[Pages.EQUITY_DETAILS] as EquityDetailsSchemaType;
  const {
    totalInterestIncome,
    totalDividendIncome,
    endingInterestReceivable,
    beginningInterestReceivable,
    totalProceeds,
    totalMarketValue,
    totalPurchaseCost,
    otherIncome: otherIncomeForm3,
    otherIncomes,
    otherEndingInterestReceivable,
    otherBeginningInterestReceivable,
  } = submissionData[Pages.INCOME_DETAILS] as IncomeDetailsSchemaType;
  const {
    totalPaymentsPurchaseSecuritiesInvestments,
    companyAdministrationFeesPeriod,
    companyAdministrationFees: companyAdministrationFeesForm4,
    portfolioManagementFeesPeriod,
    portfolioManagementFees,
    loanInterestPayments,
    beginningInterestPayable,
    endingInterestPayableLoans,
    bankCharges,
    taxWithheld,
    otherCompanyExpenses,
    otherPeriodPaidExpenses,
    otherPeriodNotPaidExpenses,
    dividendsPaidShareholders,
    dividendsNotPaidShareholders,
  } = submissionData[Pages.EXPENSE_DETAILS] as ExpenseDetailsSchemaType;
  const { accountPayableAccrual, otherLiabilities } = submissionData[Pages.LIABILITIES_DETAILS] as LiabilitiesDetailsSchemaType;
  const { fixedAssets } = submissionData[Pages.NON_CURRENT_ASSETS_DETAILS] as NonCurrentAssetsDetailsSchemaType;
  const { otherCashTransactions } = submissionData[Pages.CURRENT_ASSETS_DETAILS] as CurrentAssetsDetailsSchemaType;
  // Investment Income calculation
  const investmentIncome = toNumber(totalInterestIncome) + toNumber(totalDividendIncome) + toNumber(endingInterestReceivable) - toNumber(beginningInterestReceivable)
  const processedTotalMarketValue = toNumber(totalMarketValue)
  const marketValueMinusTotalPurchaseCost = processedTotalMarketValue - toNumber(totalPurchaseCost)
  // Net realised gain on investments calculation
  const netRealisedGainOnInvestments = processedTotalMarketValue - toNumber(securitiesValue) + toNumber(totalPaymentsPurchaseSecuritiesInvestments) - toNumber(totalProceeds) + marketValueMinusTotalPurchaseCost
  // Net unrealised gain on investments calculation
  const netUnrealisedGainOnInvestments = marketValueMinusTotalPurchaseCost
  const totalOtherIncomes = otherIncomes ? sumArray(otherIncomes, oi => toNumber(oi.amount)) : 0
  const totalOtherEndingInterestReceivable = otherEndingInterestReceivable ? sumArray(otherEndingInterestReceivable, oe => toNumber(oe.amount)) : 0
  const totalOtherBeginningInterestReceivable = otherBeginningInterestReceivable ? sumArray(otherBeginningInterestReceivable, ob => toNumber(ob.amount)) : 0
  const otherIncome = toNumber(otherIncomeForm3) + totalOtherIncomes + totalOtherEndingInterestReceivable - totalOtherBeginningInterestReceivable
  // Total income calculation
  const totalIncome = investmentIncome + netRealisedGainOnInvestments + netUnrealisedGainOnInvestments + otherIncome
  // Company administration fees calculation
  const companyAdministrationFees = toNumber(companyAdministrationFeesPeriod) + toNumber(companyAdministrationFeesForm4)
  // Portfolio management fees and related services calculation
  const portfolioManagementFeesAndRelatedServices = toNumber(portfolioManagementFeesPeriod) + toNumber(portfolioManagementFees)
  // Interest expense calculation
  const interestExpense = toNumber(loanInterestPayments) - toNumber(beginningInterestPayable) + toNumber(endingInterestPayableLoans)
  // Other expenses calculation
  const totalOtherCompanyExpenses = otherCompanyExpenses ? sumArray(otherCompanyExpenses, oc => toNumber(oc.amount)) : 0
  const totalOtherPeriodPaidExpenses = otherPeriodPaidExpenses ? sumArray(otherPeriodPaidExpenses, op => toNumber(op.amount)) : 0
  const totalOtherPeriodNotPaidExpenses = otherPeriodNotPaidExpenses ? sumArray(otherPeriodNotPaidExpenses, op => toNumber(op.amount)) : 0
  const totalFixedAssetsAssessedValue = fixedAssets ? sumArray(fixedAssets, fa => toNumber(fa.assessedValue)) : 0
  const totalFixedAssetsPurchaseCost = fixedAssets ? sumArray(fixedAssets, fa => toNumber(fa.purchaseCost)) : 0
  const totalAccountPayableAccrual = accountPayableAccrual ? sumArray(accountPayableAccrual, ap => toNumber(ap.current)) : 0
  const totalCurrentOtherLiabilities = otherLiabilities ? sumArray(otherLiabilities, ol => toNumber(ol.current)) : 0
  const totalNonCurrentOtherLiabilities = otherLiabilities ? sumArray(otherLiabilities, ol => toNumber(ol.nonCurrent)) : 0
  const otherExpenses = toNumber(bankCharges) + toNumber(taxWithheld) + totalOtherCompanyExpenses - totalOtherPeriodPaidExpenses + totalOtherPeriodNotPaidExpenses
    + (totalFixedAssetsAssessedValue - totalFixedAssetsPurchaseCost) + totalAccountPayableAccrual + totalCurrentOtherLiabilities + totalNonCurrentOtherLiabilities + toNumber(otherCashTransactions)
  // Total expense calculation
  const totalExpense = companyAdministrationFees + portfolioManagementFeesAndRelatedServices + interestExpense + otherExpenses
  // Operating Profit/(Loss) Before Tax calculation
  const operatingProfitLossBeforeTax = totalIncome - totalExpense
  // Tax calculation
  const tax = 0
  // Profit after tax calculation
  const profitAfterTax = operatingProfitLossBeforeTax + tax
  // Dividends paid calculation
  const dividendsPaid = toNumber(dividendsPaidShareholders) - toNumber(dividendsNotPaidShareholders)
  // Net Income/(Loss) calculation
  const netIncomeLoss = profitAfterTax + dividendsPaid

  return {
    investmentIncome,
    netRealisedGainOnInvestments,
    netUnrealisedGainOnInvestments,
    otherIncome,
    totalIncome,
    companyAdministrationFees,
    portfolioManagementFeesAndRelatedServices,
    interestExpense,
    otherExpenses,
    totalExpense,
    operatingProfitLossBeforeTax,
    tax,
    profitAfterTax,
    dividendsPaid,
    netIncomeLoss,
  }
}
