import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, RadioGroup, RadioGroupItem, Textarea } from "@netpro/design-system";
import { Form as RemixForm, useFetcher } from "@remix-run/react";
import { type JSX, useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { type CorporateAddressType, corporateAddressSchema } from "~/lib/simplified-tax-return/types/corporate-address/corporate-address-schema";
import { Pages } from "~/lib/simplified-tax-return/utilities/form-pages";
import { useSubmission } from "~/lib/submission/context/use-submission";
import { useScrollToError } from "~/lib/utilities/use-scroll-to-error";

export function CorporateAddress(): JSX.Element {
  const { submissionData } = useSubmission();
  const data = useMemo(() => submissionData[Pages.CORPORATE_ADDRESS] as CorporateAddressType, [submissionData]);
  const form = useForm<CorporateAddressType>({
    resolver: zod<PERSON><PERSON><PERSON>ver(corporateAddressSchema),
    shouldFocusError: false,
    defaultValues: {
      nevisAddress: "Hunkins Waterfront Plaza\nSuite 556, Main Street\nCharlestown, Nevis, West Indies.",
      recordsPlaceAddress: "",
    },
  });
  const { reset, formState } = form;
  const [canFocus, setCanFocus] = useState(true)
  useScrollToError(formState, canFocus, setCanFocus);

  function onError(): void {
    setCanFocus(true)
  }

  useEffect(() => {
    reset(data, { keepDefaultValues: true });
  }, [data, reset]);

  const fetcher = useFetcher();

  function onSubmit(data: CorporateAddressType): void {
    fetcher.submit({ data: JSON.stringify(data) }, {
      method: "post",
    });
  }

  const recordsKeptAtRegisteredOffice = form.watch("recordsKeptAtRegisteredOffice");

  return (
    <Form {...form}>
      <RemixForm onSubmit={form.handleSubmit(onSubmit, onError)} noValidate id="str-form">
        <div className="flex-col space-y-2">
          <FormField
            control={form.control}
            name="recordsKeptAtRegisteredOffice"
            render={({ field, fieldState }) => (
              <FormItem className="space-y-3">
                <FormLabel>Are the corporation's accounting records kept at the Registered Office of the corporation in Nevis? *</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    value={field.value}
                    invalid={!!fieldState.error}
                    className="flex flex-row space-x-2"
                  >
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="true" />
                      </FormControl>
                      <FormLabel className="font-normal">
                        Yes
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="false" />
                      </FormControl>
                      <FormLabel className="font-normal">
                        No
                      </FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          {recordsKeptAtRegisteredOffice === "true" && (
            <FormField
              control={form.control}
              name="nevisAddress"
              render={({ field, fieldState }) => (
                <FormItem className="w-full">
                  <FormLabel>Provide the address of the Nevis registered office *</FormLabel>
                  <FormControl>
                    <Textarea
                      invalid={!!fieldState.error}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
          {recordsKeptAtRegisteredOffice === "false" && (
            <FormField
              control={form.control}
              name="recordsPlaceAddress"
              render={({ field, fieldState }) => (
                <FormItem className="w-full">
                  <FormLabel>Provide the place of business/physical address of the corporation/directors where the records are kept *</FormLabel>
                  <FormControl>
                    <Textarea
                      invalid={!!fieldState.error}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
        </div>
      </RemixForm>
    </Form>
  )
}
