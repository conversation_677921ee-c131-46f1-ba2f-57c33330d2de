import type { LoaderFunctionArgs, TypedResponse } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import type { JSX } from "react";
import type { EntitySubmissionDetails } from "~/features/simplified-tax-return/components/forms/EntityDetailsSummary";
import { getSubmission } from "~/features/submissions/api/get-submission";
import type { FormYear } from "~/lib/simplified-tax-return/hooks/use-form-steps";
import { formSummary } from "~/lib/simplified-tax-return/utilities/form-summary";
import { SubmissionProvider } from "~/lib/submission/context/submission-context";
import { getUnflattenedDataSet } from "~/lib/submission/utilities/submission-data-set";
import { middleware } from "~/lib/middlewares.server";

export async function loader({ request, params }: LoaderFunctionArgs): Promise<TypedResponse<never> | {
  financialYear: FormYear
  submissionData: Record<string, any>
  entityDetails: EntitySubmissionDetails
}> {
  const { userId, accessToken } = await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany"], request);
  const { id } = params;

  if (!id) {
    throw new Error("Submission ID is required");
  }

  const submission = await getSubmission({ id, accessToken, userId, query: {
    includeFormDocument: true,
  } });

  if (!submission) {
    throw new Error("Submission not found");
  }

  const submissionData = getUnflattenedDataSet(submission);
  const entityDetails = {
    financialYear: submission.financialYear,
    submittedAt: submission.submittedAt,
    status: submission.statusText,
    isPaid: submission.isPaid,
  }

  return {
    financialYear: submission.financialYear,
    submissionData,
    entityDetails,
  };
}

export default function STRSummaryPDF(): JSX.Element {
  const { financialYear, submissionData, entityDetails } = useLoaderData<typeof loader>();
  const currentSummary = formSummary[financialYear];

  return (
    <SubmissionProvider submissionData={submissionData} financialYear={financialYear}>
      {currentSummary({ entityDetails })}
    </SubmissionProvider>
  );
}
