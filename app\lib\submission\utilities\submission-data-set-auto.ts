import { flatten, unflatten } from "flat";
import type { SubmissionKeyValueDTO } from "~/services/api-generated";

/**
 * TODO: This code is a temporary solution to avoid breaking other features, such as STR submissions.
 * It has been placed in a separate file to isolate its impact.
 * Once the STR code is refactored, this logic should be moved to the `submission-data-set.ts` file,
 * and this file can be safely removed.
 */

export function getDataSet(submission: SubmissionKeyValueDTO): Record<string, string | null> | null | undefined {
  if (!submission.formDocument?.revisions) {
    return {};
  }

  return submission.formDocument.revisions[0].formBuilder?.form!.dataSet
}

export function getUnflattenedDataSet(submission: SubmissionKeyValueDTO): Record<string, any> {
  return unflatten(getDataSet(submission)) as Record<string, any>;
}

export function getFlattenedSubmission(submission: Record<string, unknown>): Record<string, string> {
  const flattenedData = flatten(submission) as Record<string, string | number | boolean | Date>;

  for (const key in flattenedData) {
    if (flattenedData[key] instanceof Date) {
      flattenedData[key] = flattenedData[key].toISOString();
    } else {
      flattenedData[key] = String(flattenedData[key]);
    }
  }

  return flattenedData as Record<string, string>;
}
