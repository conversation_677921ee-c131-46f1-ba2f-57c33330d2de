import { useEffect, useState } from "react";

type Countdown = {
  days: number
  hours: number
  minutes: number
  seconds: number
};

export function useCountdown(targetDate: Date): Countdown {
  const countDownDate = new Date(targetDate).getTime();
  const [countDown, setCountDown] = useState(
    countDownDate - new Date().getTime(),
  );

  useEffect(() => {
    const updateCountDown = (): void => {
      setCountDown(countDownDate - new Date().getTime());
    };

    updateCountDown();
    const interval = setInterval(updateCountDown, 1000);

    return (): void => clearInterval(interval);
  }, [countDownDate]);

  return getReturnValues(countDown);
}

function getReturnValues(countDown: number): Countdown {
  const days = Math.floor(countDown / (1000 * 60 * 60 * 24));
  const hours = Math.floor(
    (countDown % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60),
  );
  const minutes = Math.floor((countDown % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((countDown % (1000 * 60)) / 1000);

  return { days, hours, minutes, seconds };
}
