import { z } from "zod";
import { client } from "~/lib/api-client";
import type { ClientRequestHeaders } from "~/lib/types/client-request-headers";

export const createPaymentSchema = z.object({
  legalEntityId: z.string().min(1, "Required"),
  currencyId: z.string().min(1, "Required"),
  invoiceIds: z.array(z.string()).min(1, "Required"),
});

export type createPaymentType = z.infer<typeof createPaymentSchema>;

export type CreatedPayment = {
  id: string
  legalEntityId: string
  companyName: string
  financialYear: number
  incorporationNr: string
  dateTime: string
  invoiceNr: string
  currencyId: string
  currencySymbol: string
  amount: number
  status: number
}

export function createPayment({ data, accessToken, userId }: { data: createPaymentType } & ClientRequestHeaders): Promise<CreatedPayment> {
  return client.post(
    "/client/payments",
    accessToken,
    userId,
    data,
  );
}
