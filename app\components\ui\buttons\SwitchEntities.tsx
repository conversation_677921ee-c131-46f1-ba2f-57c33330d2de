import type { JS<PERSON> } from "react";
import { useState } from "react";
import { useRouteLoaderData } from "@remix-run/react";
import { SwitchMasterClient } from "~/components/ui/buttons/SwitchMasterClient";
import { SwitchCompany } from "~/components/ui/buttons/SwitchCompany";
import type { RootLoaderData } from "~/lib/auth/types/session-type";

export function SwitchEntities(): JSX.Element | null {
  const [openMasterClientDialog, setOpenMasterClientDialog] = useState(false);
  const [openCompanyDialog, setOpenCompanyDialog] = useState(false);
  const rootLoader = useRouteLoaderData<RootLoaderData>("root");

  if (!rootLoader) {
    return null;
  }

  const {
    currentMasterClient,
    currentCompany,
    totalMasterClients,
    totalMasterClientCompanies,
  } = rootLoader.sessionData;

  return (
    <>
      <SwitchMasterClient
        open={openMasterClientDialog}
        current={currentMasterClient}
        total={totalMasterClients}
        setOpenDialog={setOpenMasterClientDialog}
        onChange={isChanged => isChanged && setOpenCompanyDialog(true)}
      />

      <SwitchCompany
        open={openCompanyDialog}
        setOpenDialog={setOpenCompanyDialog}
        current={currentCompany}
        total={totalMasterClientCompanies}
        onChangeMasterClient={() => setOpenMasterClientDialog(true)}
      />
    </>
  );
}
