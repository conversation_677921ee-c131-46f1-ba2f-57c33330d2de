import type { LoaderFunctionArgs, TypedResponse } from "@remix-run/node";
import { Link, useLoaderData } from "@remix-run/react";
import type { JSX } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Separator } from "@netpro/design-system";
import { ChevronRight, Download, Landmark } from "lucide-react";
import { Breadcrumb } from "~/components/breadcrumbs/Breadcrumb";
import { getSubmission } from "~/features/submissions/api/get-submission";
import { SubmissionStatusNames } from "~/lib/submission/utilities/submission-status";
import { middleware } from "~/lib/middlewares.server";

const title = "Submission for" as const;
const breadCrumbList = [
  {
    href: "/",
    name: "Simplified Tax Returns",
  },
];

export const handle = {
  breadcrumb: (): JSX.Element => <Breadcrumb data={breadCrumbList} />,
  title,
};

export async function loader({ request, params }: LoaderFunctionArgs): Promise<TypedResponse<never> | {
  companyName: string
  submissionId: string
  isPaid: boolean | undefined
  invoiceId: string | undefined
}> {
  const { accessToken, userId, company: currentCompany } = await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany", "requireStrModule"], request);
  const { id } = params;

  if (!id) {
    throw new Error("Submission ID is required");
  }

  const submission = await getSubmission({
    id,
    accessToken,
    userId,
    query: { includeFormDocument: true },
  });

  // Validate finalize step and status
  if (submission.statusText !== SubmissionStatusNames.Submitted) {
    throw new Error("Submission is not in submitted status");
  }

  return { companyName: currentCompany.companyName, submissionId: id, invoiceId: submission.invoiceId, isPaid: submission.isPaid };
}

export default function FinalizeContainer(): JSX.Element {
  const { submissionId, invoiceId, isPaid } = useLoaderData<typeof loader>();

  return (
    <div className="w-full flex flex-col gap-5 px-4 pt-1 pb-5">
      <div className="flex flex-col gap-1">
        <p>
          Thank you for submitting your declaration.
        </p>
        <p>
          Click download for the summary of the declaration
          {isPaid || !invoiceId ? "." : " or proceed to pay to complete your submission."}
        </p>
      </div>
      {!isPaid && (
        <Alert variant="info" title="Important note">
          {invoiceId
            ? "Please note that the declaration will not be submitted by your Registered Agent to the tax office until payment is received."
            : "The declaration will be submitted to the tax office upon payment of the annual invoice. If the invoice has already been paid, the declaration will be processed automatically, and no further action is needed."}
        </Alert>
      )}
      <div className="flex gap-x-2">
        <Button type="button" size="sm" asChild>
          <Link to={`/simplified-tax-return/${submissionId}/summary`} target="_blank">
            <Download className="size-4 mr-2 text-white" />
            Download
          </Link>
        </Button>
        {invoiceId && (
          <Button type="button" size="sm" asChild>
            <Link to={`/invoices/${invoiceId}/file`} target="_blank">
              <Landmark className="size-4 mr-2 text-white" />
              Proforma Invoice
            </Link>
          </Button>
        )}
      </div>
      <div>
        <Separator />
        <div className="flex justify-end space-x-2 py-4">
          {isPaid || !invoiceId
            ? (
                <Button type="button" asChild>
                  <Link to="/simplified-tax-return/submissions">
                    Back to submissions
                    <ChevronRight className="size-4 ml-2 text-white" />
                  </Link>
                </Button>
              )
            : (
                <Button type="button" asChild>
                  <Link to="/payments/pending">
                    Proceed to Pending Payments
                    <ChevronRight className="size-4 ml-2 text-white" />
                  </Link>
                </Button>
              )}
        </div>
      </div>
    </div>
  );
}
