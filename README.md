<p align="center">
  <a href="https://www.netprogroup.com/">
    <img alt="NetPro Group" src="https://www.netprogroup.com/assets/files/logo-blauw.svg" width="200" />
  </a>
</p>

# Trident Trust - Private Client Portal (PCP)

This is the repository for the Private Client Portal (PCP) project for Trident Trust.

## Documentation & references

- [NodeJS version](./docs/node.md)
- [Remix instructions](./docs/remix.md)
- [NPM configuration](./docs/npm.md)

## Instructions to run project

1. **For Windows users:**

    Connect to the NetPro internal NPM registry by running the following command from the project root:

    ```sh
    npx vsts-npm-auth -config .npmrc
    ```

    This will install the vsts-npm-auth package and run it to authenticate with the NetPro NPM registry.
1. Run the following command to install all necessary dependencies:

    ```sh
    npm install
    ```

1. If you don't have an environment file, copy the file `.env.local` to `.env`.

1. Run the following command to start the project in development mode:

    ```sh
    npm run dev
    ```

## Deployment

First, build your app for production:

```sh
npm run build
```

Then run the app in production mode:

```sh
npm start
```

Now you'll need to pick a host to deploy it to.

### DIY

If you're familiar with deploying Node applications, the built-in Remix app server is production-ready.

Make sure to deploy the output of `npm run build`

- `build/server`
- `build/client`

## Styling

This project comes with [Tailwind CSS](https://tailwindcss.com/) already configured for a simple
default starting experience.

# Notes for Developers

## Function naming conventions

- **Function names should be descriptive and follow the camelCase naming convention.**
- **Function names should start with a verb.**
- **Function names should be in the present tense.**
- **Function names that start with "get" return the expected value or undefined.**
- **Function names that start with "find" return the expected value or null.**
- **Function names that start with "is" return a boolean value.**
- **Function names that start with "has" return a boolean value.**
- **Function names that start with "require" return the expected value or throw an error.**

# Release management

See [wiki page in this project](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_wiki/wikis/Trident-Trust---Private-Client-Portal.wiki/151/Release-Management-(Client-Management-portal))

# API Integration

Backend services are automatically generated using `hey-api`. Unfortunately, due to the constraints of the Azure environment, services cannot be automatically generated in production and must be manually generated by the developer. If you find that some backend services are missing in your project, follow these steps:

1. Ensure your local backend is up-to-date and running. Confirm that the required service(s) are available in the [Swagger documentation](https://localhost:7108/swagger/index.html), as this will be used to generate the services for the frontend.
2. Run the command: `npm run generate-api-service`.
3. **Important:** Do not reformat or manually modify the code in `app/services/api-generated/**` as it will be overwritten by future runs.

### Handling Merge Conflicts

Merge conflicts may occur due to this process. If this happens, simply delete the existing generated services and regenerate them, ensuring you are using the latest version of the local API.
