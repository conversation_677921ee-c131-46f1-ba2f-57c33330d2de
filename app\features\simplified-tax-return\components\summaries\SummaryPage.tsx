import type { JSX, ReactNode } from "react";
import SummaryFooter from "./SummaryFooter";
import SummaryHeader from "./SummaryHeader";
import Page from "~/components/pages/Page";
import PageContent from "~/components/pages/PageContent";
import PageFooter from "~/components/pages/PageFooter";
import PageHeader from "~/components/pages/PageHeader";

export function SummaryPage({ pageNumber, children }: { pageNumber: number, children: ReactNode }): JSX.Element {
  return (
    <Page>
      <PageHeader>
        <SummaryHeader />
      </PageHeader>
      <PageContent>
        <div className="font-inter">
          {children}
        </div>
      </PageContent>
      <PageFooter>
        <div className="flex">
          <div className="flex grow">
            <SummaryFooter />
          </div>
          <span className="text-sm font-inter text-right">
            {`PAGE ${pageNumber}`}
          </span>
        </div>
      </PageFooter>
    </Page>
  );
}
