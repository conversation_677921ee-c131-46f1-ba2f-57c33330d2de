import type { InputProps } from "@netpro/design-system";
import { Input, cn } from "@netpro/design-system";
import type { ForwardedRef, ReactNode } from "react";
import React from "react";

export type Props = {
  currencyName: string
} & InputProps;

export const CurrencyInput = React.forwardRef<HTMLInputElement, Props>(
  ({ currencyName, ...props }, ref: ForwardedRef<HTMLInputElement>): ReactNode => {
    return (
      <div className="flex items-stretch">
        <span className="flex items-center border-[1.2px] border-gray-300 px-4 bg-gray-100 rounded-l-lg border-r-0 shadow-sm">
          {currencyName}
        </span>
        <Input
          {...props}
          ref={ref}
          className={cn("rounded-l-none", props.className)}
        />
      </div>
    );
  },
);

CurrencyInput.displayName = "CurrencyInput";
