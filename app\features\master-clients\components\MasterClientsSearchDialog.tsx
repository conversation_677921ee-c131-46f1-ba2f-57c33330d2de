import { Dialog, DialogContent } from "@netpro/design-system";
import { useFetcher } from "@remix-run/react";
import type { ReactNode } from "react";
import MasterClientsSearch from "./MasterClientsSearch";
import type { MasterClientsSearchResultDTO } from "~/services/api-generated";

type MasterClientSearchDialogProps = {
  open: boolean
  setOpen: (isOpen: boolean) => void
  currentMasterClientId: string
  onChange: (masterClientChanged: boolean) => void
}

export default function MasterClientSearchDialog({
  open,
  setOpen,
  currentMasterClientId,
  onChange,
}: MasterClientSearchDialogProps): ReactNode {
  const fetcher = useFetcher<any>();
  const handleSelectMasterClient = (selectedMasterClient: MasterClientsSearchResultDTO): void => {
    fetcher.submit(
      {
        id: selectedMasterClient.masterClientId as string,
        actionType: "change",
      },
      { method: "post", action: "/api/master-clients" },
    );
    onChange(selectedMasterClient.masterClientId !== currentMasterClientId);
  }
  const handleOpenChange = (isOpen: boolean): void => {
    setOpen(isOpen);
  };

  return (
    <Dialog
      modal
      open={open}
      onOpenChange={(isOpen) => {
        handleOpenChange(isOpen);
      }}
    >
      <DialogContent className="max-w-screen-md">
        <MasterClientsSearch
          initialValues={null}
          onSelect={handleSelectMasterClient}
        />
      </DialogContent>
    </Dialog>
  )
}
