import { But<PERSON> } from "@netpro/design-system";
import { type ErrorResponse, <PERSON> } from "@remix-run/react";
import type { ReactNode } from "react";

const LogoutAction = (
  <form action="/logout" method="post">
    <Button type="submit">Logout</Button>
  </form>
)
const SwitchAccountAction = (
  <form action="/logout" method="post">
    <Button type="submit">
      Switch Account
    </Button>
  </form>
)
const BackToDashboardAction = (
  <Button asChild>
    <Link to="/dashboard">
      Go back to dashboard
    </Link>
  </Button>
)

export function ErrorContainer({ error }: { error: ErrorResponse }): ReactNode {
  const { status } = error;
  let { statusText: title, data: message } = error;
  let action = BackToDashboardAction
  switch (status) {
    case 400:
      title = "Oops! Something Went Wrong";
      message = (
        <p>
          Oops! It looks like there was a problem with your request. It seems
          that there&apos; an issue with the information provided. Please review
          your input and try again.
        </p>
      );
      break;
    case 401:
      title = "Unauthorized Access";
      message = (
        <p>
          Oops! It looks like you&apos;re trying to access a resource without proper
          authorization. Access to this content requires valid credentials.
          Please make sure you are logged in with the correct account or contact
          the administrator for assistance
        </p>
      );
      action = SwitchAccountAction;
      break;
    case 403:
      title = error.data ?? "Access denied";
      message = (
        <p>
          We&apos;re sorry, but you don&apos;t have permission to access this page. It
          seems that you&apos;re trying to access content that is restricted to
          authorized users only.
        </p>
      );
      action = LogoutAction
      break;
    case 412:
      title = "Sorry, you don't have permission to view this page.";
      message = (
        <div className="text-start">
          <p>
            <strong>Reason</strong>
            : Your user roles have not been defined which
            means we can&apos;t verify your access level.
          </p>
          <div>
            <strong>Please take the following steps:</strong>
            <ol className="list-decimal pl-5">
              <li>
                <strong>Contact your system administrator: </strong>
                Reach out to
                your system administrator to have your user roles set up
                properly.
              </li>
              <li>
                <strong>Switch account: </strong>
                If you have another account with the correct roles, you can switch to that account to continue.
              </li>
            </ol>
          </div>
        </div>
      );
      action = SwitchAccountAction
      break;
    case 423:
      title = "The user does not have access to the platform";
      message = (
        <div className="text-start">
          <p>
            <strong>Reason</strong>
            : Your account has been retired.
          </p>
          <p>
            <strong>Please take the following step: </strong>
            Reach out to  your system administrator to activate your account.

          </p>
        </div>
      );
      action = LogoutAction
      break;
    case 424:
      title = "Your user account was not found.";
      message = (
        <div className="text-start">
          <p>
            <strong>Reason</strong>
            : Your account is no longer part of the Trident Trust - Private Client Portal.
          </p>
        </div>
      );
      action = LogoutAction
      break;
    default:
  }

  return (
    <ErrorContent
      status={status}
      title={title}
      message={message}
      action={action}
    />
  );
}

function ErrorContent({
  status,
  title,
  message,
  action,
}: {
  status: number
  title: string
  message: ReactNode
  action: ReactNode
}): JSX.Element {
  return (
    <>
      <p className="text-base font-semibold text-primary">{status}</p>
      <h1 className="mt-4 text-3xl font-bold tracking-tight text-gray-900 sm:text-5xl">
        {title}
      </h1>
      <div className="mt-6 text-base leading-7 text-gray-600">{message}</div>
      <div className="mt-10 flex items-center justify-center gap-x-6">
        {action}
      </div>
    </>
  );
}
