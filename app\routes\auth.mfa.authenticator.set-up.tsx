import type { ActionFunctionArgs, LoaderFunctionArgs, MetaFunction, TypedResponse } from "@remix-run/node";
import { json, redirect, useLoaderData, useSubmit } from "@remix-run/react";
import QRCode from "qrcode";
import { useForm } from "react-hook-form";
import type { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Button,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
  Input,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@netpro/design-system";
import { Form as RemixForm } from "@remix-run/react/dist/components";
import { Method } from "~/lib/auth/utils/mfa";
import { commitSession, getSession, getSessionData } from "~/lib/auth/utils/session.server";
import { mfaCodeSchema, type mfaCodeType, verifyMfaCode } from "~/features/mfa/api/verify-mfa-code";
import { middleware } from "~/lib/middlewares.server";

const title = "Authenticator set up" as const;

export const meta: MetaFunction = () => [
  { title },
  { name: "Authenticator set up page", content: title },
];

export async function loader({ request }: LoaderFunctionArgs): Promise<TypedResponse<never> | {
  qrCode: string
  mfaAuthenticatorSecret: string
}> {
  await middleware(["auth"], request);
  const { mfaMethod, mfaCompleted } = await getSessionData(request);
  if (mfaMethod !== Method.None || mfaCompleted) {
    return redirect("/dashboard");
  }

  const session = await getSession(request.headers.get("Cookie"));
  const mfaAuthenticatorQRUrl = session.get("mfaAuthenticatorQRUrl") as string | undefined;
  const mfaAuthenticatorSecret = session.get("mfaAuthenticatorSecret") as string | undefined;

  // Information needed to set up MFA Authenticator is missing, redirect to set up MFA setup
  if (!mfaAuthenticatorQRUrl || !mfaAuthenticatorSecret) {
    return redirect("/auth/mfa/set-up", { headers: { "Set-Cookie": await commitSession(session) } });
  }

  const qrCode = await QRCode.toDataURL(mfaAuthenticatorQRUrl);

  return {
    qrCode,
    mfaAuthenticatorSecret,
  };
}

export async function action({ request }: ActionFunctionArgs): Promise<TypedResponse<null> | undefined> {
  const { userId, accessToken } = await middleware(["auth"], request);
  const { mfaMethod, mfaCompleted } = await getSessionData(request);
  if (mfaMethod !== Method.None || mfaCompleted) {
    // MFA method is not Authenticator, redirect to the dashboard
    return redirect("/dashboard");
  }

  const session = await getSession(request.headers.get("Cookie"));

  try {
    const formData = await request.formData();
    const data = JSON.parse(formData.get("data") as string) as mfaCodeType;
    mfaCodeSchema.parse(data);
    const verificationResult = await verifyMfaCode({ data: {
      code: data.code.trim(),
    }, accessToken, userId });

    if (verificationResult.success) {
      session.flash("notification", { title: "MFA method set up", message: "MFA method set up successfully", variant: "success" });
      session.set("mfaCompleted", true);

      return redirect("/dashboard", { headers: { "Set-Cookie": await commitSession(session) } });
    } else {
      session.flash("notification", { title: "An error has occurred", message: "Invalid code, please try again", variant: "error" });

      return json(null, { status: 400, headers: { "Set-Cookie": await commitSession(session) } });
    }
  } catch (error) {
    const err = error as Response;
    const errorMessage = await err.text();

    if (errorMessage) {
      session.flash("notification", { title: "An error has occurred", message: errorMessage, variant: "error" });

      return json(null, { status: err.status, headers: { "Set-Cookie": await commitSession(session) } });
    }
  }
}

export default function MFAAuthenticator(): JSX.Element {
  const { qrCode, mfaAuthenticatorSecret } = useLoaderData<typeof loader>();
  const submit = useSubmit();
  const form = useForm<z.infer<typeof mfaCodeSchema>>({
    resolver: zodResolver(mfaCodeSchema),
    defaultValues: {
      code: "",
    },
  });

  function onSubmit(data: z.infer<typeof mfaCodeSchema>): void {
    submit({ data: JSON.stringify(data) }, { method: "POST" });
  }

  return (
    <div className="mt-5">
      <div>
        <p>
          Scan the QR code below or type the secret key into your authenticator app and then input
          the code generated by the app.
        </p>
        <Tooltip delayDuration={0}>
          <TooltipTrigger asChild>
            <Button className="pl-0" variant="link">Need help setting up the authenticator?</Button>
          </TooltipTrigger>
          <TooltipContent className="w-96 p-5" side="right">
            <span className="font-bold">
              1. Download an Authenticator App
            </span>
            <p className="mb-2">
              Begin by downloading a compatible authenticator app to your smartphone.
              You can choose from popular options like Google Authenticator, Microsoft Authenticator, or 1Password.
              These apps are available for free on the Google Play Store (for Android devices) and the Apple App Store (for iOS devices).
            </p>
            <span className="font-bold">
              2. Scan the QR Code or Enter a Setup Key
            </span>
            <p className="mb-2">
              After installing the app, open it and select the option to add a new account.
              You&rsquo;ll typically have two options: scan a QR code or enter a setup key manually.
              Use the QR code or secret key below to add your account to the app.
            </p>
            <span className="font-bold">
              3. Verify the Generated Code
            </span>
            <p className="mb-2">
              Once you&rsquo;ve added your account to the authenticator app, the app will begin
              generating time-based codes. Enter the current code displayed in the app into the
              corresponding field on your account&rsquo;s MFA setup page to verify the setup.
            </p>
            <span className="font-bold">
              4. Complete the Setup
            </span>
            <p className="mb-2">
              After successfully verifying the code, your MFA setup is complete. From now on, whenever
              you log in, you&rsquo;ll need to enter a code from your authenticator app.
            </p>
          </TooltipContent>
        </Tooltip>
      </div>
      <div className="flex justify-center">
        <img src={qrCode} alt="QR Code" className="w-48" />
      </div>
      <div className="flex justify-center">
        <span className="text-center">
          <span className="font-bold mr-2">
            Secret key:
          </span>
          <span>
            {mfaAuthenticatorSecret}
          </span>
        </span>
      </div>
      <Form {...form}>
        <RemixForm onSubmit={form.handleSubmit(onSubmit)} className="pt-6" noValidate>
          <FormField
            control={form.control}
            name="code"
            render={({ field, fieldState }) => (
              <FormItem>
                <FormControl>
                  <Input
                    invalid={!!fieldState.error}
                    placeholder="Authentication code"
                    className="rounded-none ring-0 border-b border-grey-500 h-8 hover:border-grey-700 focus:border-grey-700 hover:ring-0 focus:ring-0"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <div className="flex flex-row justify-end w-full mt-3">
            <Button type="submit" className="rounded-none h-8 bg-[#0067b8] font-normal px-6">Submit</Button>
          </div>
        </RemixForm>
      </Form>
    </div>
  )
}
