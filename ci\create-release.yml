trigger: none

pool:
  vmImage: ubuntu-latest

stages:
  - stage: Changelog
    # Only execute from the main branch
    condition: eq(variables['build.sourceBranch'], 'refs/heads/main')
    jobs:
      - job: Changelog
        displayName: 'Create changelog'
        steps:
          - checkout: self
            clean: true
            persistCredentials: true
            # A deep fetch is required to get all data in order to generate the changelog properly.
            fetchDepth: 0
            displayName: 'Checkout repository'

          - task: NodeTool@0
            inputs:
              versionSource: 'fromFile'
              versionFilePath: './.nvmrc'
              checkLatest: true
            displayName: 'Install Node.js'

          - task: Npm@1
            inputs:
              command: 'install'
              workingDir: '.'
              verbose: true
            displayName: 'Install dependencies'

          - script: |
              git config --global user.email "<EMAIL>"
              git config --global user.name "Azure Build Pipeline"
            displayName: 'Set git user'

          - task: Npm@1
            inputs:
              command: 'custom'
              workingDir: '.'
              # Create an actual release version and git tag
              customCommand: 'run release'
            displayName: 'Create CHANGELOG.md and bump version'

          # Pull the latest changes from both main and develop before pushing
          - script: |
              git pull origin main --rebase
              git pull origin develop --rebase
            displayName: 'Pull latest changes from main and develop with rebase'

          # Push the changelog and updated package.json files
          - script: |
              git push --follow-tags origin HEAD:main HEAD:develop
            displayName: 'Push newly created changelog to main and develop branch'

