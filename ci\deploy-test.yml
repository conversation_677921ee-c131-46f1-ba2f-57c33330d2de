trigger: none

variables:
  APP_SERVICE_CONNECTION: 'DEV Infra Service Connection'
  APP_SERVICE_NAME: 'app-pcp-clt-dev-eus2'
  RESOURCE_GROUP: 'rg-npdev-pcp-app-eus2'

stages:
  - stage: Build
    # Only execute from the main branch
    condition: eq(variables['build.sourceBranch'], 'refs/heads/main')
    pool:
      vmImage: ubuntu-latest
    jobs:
      - job: Changelog
        displayName: 'Create changelog'
        steps:
          - checkout: self
            clean: true
            persistCredentials: true
            # A deep fetch is required to get all data in order to generate the changelog properly.
            fetchDepth: 0
            displayName: 'Checkout repository'

          - task: NodeTool@0
            inputs:
              versionSource: 'fromFile'
              versionFilePath: './.nvmrc'
              checkLatest: true
            displayName: 'Install Node.js'

          - task: Npm@1
            inputs:
              command: 'install'
              workingDir: '.'
              verbose: true
            displayName: 'Install dependencies'

          - script: |
              git config --global user.email "<EMAIL>"
              git config --global user.name "Azure Build Pipeline"
            displayName: 'Set git user'

          - task: Npm@1
            inputs:
              command: 'custom'
              workingDir: '.'
              customCommand: 'run release -- --prerelease beta'
            displayName: 'Create CHANGELOG.md and bump version'

          - script: |
              git push --follow-tags origin HEAD:main HEAD:develop
            displayName: 'Push newly created changelog to main and develop'

      - job: BuildApplication
        dependsOn: Changelog
        displayName: 'Build application'
        steps:
          - template: templates/build-application.yml

  - stage: Deploy
    # Only execute from the main branch
    condition: and(succeeded('Build'), eq(variables['build.sourceBranch'], 'refs/heads/main'))
    dependsOn: Build
    pool:
      name: TT PCP - LinuxAgents dev
    jobs:
    - deployment: DeployJob
      displayName: 'Deploy to Test'
      environment: 'Testing'  # Adjust if you have specific environments set up in Azure DevOps
      strategy:
        runOnce:
          deploy:
            steps:
              - script: |
                  echo "===== Debugging context ======"
                  echo "> Pipeline.Workspace contents:"
                  cd $(Pipeline.Workspace)
                  ls -la
                  echo "-----"
                  echo "> ./app folder contents:"
                  cd app
                  ls -la
                  echo "===== End of context ======"
              # Configure app settings for run from package deployment
              - task: AzureAppServiceSettings@1
                displayName: 'Configure App Settings'
                inputs:
                  azureSubscription: '${{ variables.APP_SERVICE_CONNECTION }}'
                  appName: '${{ variables.APP_SERVICE_NAME }}'
                  resourceGroupName: '${{ variables.RESOURCE_GROUP }}'
                  # slotName: 'production'
                  appSettings: |
                    [
                      {
                        "name": "WEBSITE_RUN_FROM_PACKAGE",
                        "value": "1",
                        "slotSetting": true
                      }
                    ]
              - task: AzureWebApp@1
                displayName: 'Azure App Service Deploy: ${{ variables.APP_SERVICE_NAME }}'
                inputs:
                  azureSubscription: '${{ variables.APP_SERVICE_CONNECTION }}'
                  appType: 'webAppLinux'
                  appName: '${{ variables.APP_SERVICE_NAME }}'
                  deployToSlotOrASE: false
                  resourceGroupName: '${{ variables.RESOURCE_GROUP }}'
                  #SlotName: production
                  package: '$(Pipeline.Workspace)/app/package.zip'
                  runtimeStack: 'NODE|20-lts'
                  startUpCommand: 'node ./node_modules/.bin/remix-serve ./build/server/index.js'
                  deploymentMethod: zipDeploy