import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>ider, notify } from "@netpro/design-system";
import type { HeadersFunction, LinksFunction, LoaderFunctionArgs, MetaArgs, MetaFunction, TypedResponse } from "@remix-run/node";
import { json } from "@remix-run/node";
import {
  Link,
  Links,
  Meta,
  Outlet,
  Scripts,
  ScrollRestoration,
  isRouteErrorResponse,
  useNavigate,
  useRouteError,
  useRouteLoaderData,
} from "@remix-run/react";
import { type JSX, type ReactNode, useEffect, useMemo } from "react";
import { ErrorContainer } from "./components/errors/ErrorContainer";
import { useNonce } from "./lib/utilities/use-nonce";
import type { SessionContextValue } from "./components/session-context";
import { SessionContext } from "./components/session-context";
import { commitSession, getSession } from "~/lib/auth/utils/session.server";
import stylesheet from "~/default.css?url";
import { defaultHeaders, defaultLinks, defaultMeta } from "~/lib/config";
import type { CompanyModuleState } from "~/features/companies/api/get-company-module-state";
import { DEFAULT_MODULE_STATE } from "~/features/companies/api/get-company-module-state";

export const links: LinksFunction = () => [
  ...defaultLinks,
  { rel: "stylesheet", href: stylesheet },
];

export const meta: MetaFunction = ({ error }: MetaArgs) => [
  ...defaultMeta(error ? "Error" : undefined),
];

export const headers: HeadersFunction = () => ({
  ...defaultHeaders(),
});

const loginRoute = "/login";
const publicRoutes = ["/", loginRoute, "/auth/callback", "/auth/verify-mcc"];

export async function loader({ request }: LoaderFunctionArgs): Promise<TypedResponse<{
  notification: any
  sessionData: {
    userId: any
    userName: any
    userEmail: any
    currentMasterClient: any
    currentCompany: any
    companyModules: CompanyModuleState | undefined
    totalMasterClients: any
    totalMasterClientCompanies: any
    queuedMasterClientUpdate: any
  }
}> | null> {
  const url = new URL(request.url);
  // This are public routes, no need to check for authentication
  if (publicRoutes.includes(url.pathname)) {
    return null;
  }

  const session = await getSession(request.headers.get("Cookie"));
  const {
    userId,
    userName,
    userEmail,
    currentMasterClient,
    currentCompany,
    companyModules,
    totalMasterClients,
    totalMasterClientCompanies,
    queuedMasterClientUpdate,
  } = session.data;
  const notification = session.get("notification");

  return json({
    notification,
    sessionData: {
      userId,
      userName,
      userEmail,
      currentMasterClient,
      currentCompany,
      companyModules,
      totalMasterClients,
      totalMasterClientCompanies,
      queuedMasterClientUpdate,
    },
  }, { headers: { "Set-Cookie": await commitSession(session) } });
}

export default function App(): ReactNode {
  const data = useRouteLoaderData<typeof loader>("root");
  const { nonce } = useNonce();

  useEffect(() => {
    if (data?.notification) {
      notify(data.notification);
    }
  }, [data]);

  const sessionContextValue = useMemo((): SessionContextValue => ({
    company: data?.sessionData.currentCompany,
  }), [data?.sessionData.currentCompany])

  return (
    <html lang="en" className="h-full antialiased">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <Meta />
        <Links />
      </head>
      <body className="h-full">
        <SessionContext.Provider value={sessionContextValue}>
          <TooltipProvider>
            <NotificationProvider />
            <Outlet context={{
              companyModules: data?.sessionData.companyModules ?? DEFAULT_MODULE_STATE,
            }}
            />
          </TooltipProvider>
          <ScrollRestoration nonce={nonce} />
          <Scripts nonce={nonce} />
        </SessionContext.Provider>
      </body>
    </html>
  );
}

export function ErrorBoundary(): JSX.Element {
  const error = useRouteError();
  const navigate = useNavigate();

  useEffect(() => {
    if (isRouteErrorResponse(error)) {
      // If the session expired redirect to login
      if (error.status === 401) {
        navigate(loginRoute);
      }
    }
  }, [navigate, error]);

  return (
    <html lang="en" className="h-full antialiased">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <Meta />
        <Links />
      </head>
      <body className="h-full flex items-center">
        <div className="max-w-xl drop-shadow-xl mx-auto px-10 py-12 rounded-xl">
          {isRouteErrorResponse(error)
            ? <ErrorContainer error={error} />
            : (
                <>
                  <h1 className="mt-4 text-3xl font-bold tracking-tight text-gray-900 sm:text-5xl">
                    Something went wrong
                  </h1>
                  <p className="mt-6 text-base leading-7 text-gray-600">
                    Please try again or contact support if the issue continues.
                  </p>
                  <div className="mt-10 flex items-center justify-center gap-x-6">
                    <Button asChild>
                      <Link to="/dashboard">Go back to Dashboard</Link>
                    </Button>
                  </div>
                </>
              )}
        </div>
      </body>
    </html>
  );
}
