import { <PERSON><PERSON>, <PERSON>roll<PERSON><PERSON>, <PERSON>rollBar, Table, TableBody, TableCell, TableHead, TableHeader, TableRow, Tooltip, TooltipContent, TooltipTrigger } from "@netpro/design-system";
import { Info, Pencil, X } from "lucide-react";
import type { ReactNode } from "react";
import type { AssetSchemaType } from "~/lib/basic-financial-report/types/non-current-assets-details-schema";

type Props = {
  assets: (AssetSchemaType & { formArrayId: string })[]
  onSelect: (income: AssetSchemaType, index: number) => void
  onDelete: (index: number) => void
  disabled: boolean
}

export function AssetTable({
  assets,
  onSelect,
  onDelete,
  disabled,
}: Props): ReactNode {
  return (
    <div className="border-gray-200 border mt-4">
      <ScrollArea>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Description</TableHead>
              <TableHead>Purchase Year</TableHead>
              <TableHead>Purchase Cost</TableHead>
              <TableHead>
                <Tooltip delayDuration={0}>
                  <p className="flex items-center gap-1">
                    Assessed Value
                    <TooltipTrigger asChild>
                      <Info className="flex shrink-0 size-4" />
                    </TooltipTrigger>
                  </p>
                  <TooltipContent className="w-96 p-5 space-y-3 font-inter" side="right">
                    <p>
                      If Assessed Value is not available, please indicate the Purchase Cost.
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TableHead>
              <TableHead></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {!assets.length && (
              <TableRow>
                <TableCell colSpan={5} className="text-center text-gray-500">
                  No assets available
                </TableCell>
              </TableRow>
            )}
            {assets.length > 0 && assets.map((asset, index) => (
              <TableRow key={asset.formArrayId}>
                <TableCell>{asset.description}</TableCell>
                <TableCell>{asset.purchaseYear}</TableCell>
                <TableCell>{`$ ${asset.purchaseCost}`}</TableCell>
                <TableCell>{`$ ${asset.assessedValue}`}</TableCell>
                <TableCell className="flex justify-end gap-2">
                  <Button type="button" size="sm" variant="secondary" onClick={() => onSelect(asset, index)} disabled={disabled}>
                    <Pencil className="mr-2 size-4" />
                    Edit
                  </Button>
                  <Button type="button" size="sm" variant="destructive" onClick={() => onDelete(index)} disabled={disabled}>
                    <X className="mr-2 size-4" />
                    Remove
                  </Button>
                </TableCell>
              </TableRow>
            ))}
            <TableRow>
              <TableCell className="font-semibold" colSpan={2}>TOTAL</TableCell>
              <TableCell className="font-semibold">{`$ ${assets.reduce((acc, cur) => acc + Number(cur.purchaseCost), 0)}`}</TableCell>
              <TableCell className="font-semibold">{`$ ${assets.reduce((acc, cur) => acc + Number(cur.assessedValue), 0)}`}</TableCell>
              <TableCell />
            </TableRow>
          </TableBody>
        </Table>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>
    </div>
  )
}
