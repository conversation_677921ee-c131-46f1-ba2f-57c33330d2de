import type { JS<PERSON> } from "react";
import { useState } from "react";
import { SwitchEntityButton } from "~/components/ui/buttons/SwitchEntityButton";
import { SwitchEntityTitle } from "~/components/ui/buttons/SwitchEntityTitle";
import { SwitchEntitySubtitle } from "~/components/ui/buttons/SwitchEntitySubtitle";
import type { CompanyDTO } from "~/lib/auth/types/session-type";
import CompaniesSearchDialog from "~/features/companies/components/CompaniesSearchDialog";

type SwitchCompanyProps = {
  current?: CompanyDTO
  total?: number | undefined
  onChangeMasterClient?: () => void
  open?: boolean
  setOpenDialog: (open: boolean) => void
};

export function SwitchCompany({
  current,
  onChangeMasterClient,
  total = 0,
  open = false,
  setOpenDialog,
}: SwitchCompanyProps): JSX.Element | null {
  const [currentCompany, setCurrentCompany] = useState<CompanyDTO | undefined>(current);

  function handleChangeMasterClient(): void {
    onChangeMasterClient?.();
  }

  return (
    <>
      <SwitchEntityButton onClick={() => setOpenDialog(true)}>
        <SwitchEntityTitle>
          {currentCompany?.companyName
          || (total > 1
            ? "Please select a company"
            : "No companies available")}
        </SwitchEntityTitle>
        <SwitchEntitySubtitle prefix="Jurisdiction">
          {currentCompany?.jurisdictionName}
        </SwitchEntitySubtitle>
      </SwitchEntityButton>

      <CompaniesSearchDialog
        open={open}
        setOpen={setOpenDialog}
        onChange={(selectedCompany) => {
          selectedCompany.incorporationNumber = selectedCompany.incorporationNumber || "Unknown";
          setCurrentCompany(selectedCompany);
        }}
        onChangeMasterClient={handleChangeMasterClient}
      />
    </>
  );
}
