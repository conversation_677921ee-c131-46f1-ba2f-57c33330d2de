import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Dialog, DialogContent, Dialog<PERSON>ooter, DialogHeader, Form, FormControl, FormField, FormItem, FormLabel, FormMessage, Input, RadioGroup, RadioGroupItem, ScrollArea, ScrollBar, Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@netpro/design-system";
import type { UseFormReturn } from "react-hook-form";
import type { JSX } from "react";
import { BusinessActivity, type BusinessActivityType } from "~/lib/simplified-tax-return/types/business-activity/2019-2024/business-activity-schema";

type BusinessActivityDialogProps = {
  businessActivity?: BusinessActivityType
  open: boolean
  year: number
  setOpen: (open: boolean) => void
  onDelete?: () => void
  onSubmit: (data: BusinessActivityType) => void
  form: UseFormReturn<BusinessActivityType>
};

export function BusinessActivityDialog({ businessActivity, open, year, setOpen, onSubmit, onDelete, form }: BusinessActivityDialogProps): JSX.Element {
  const watchActivity = form.watch("activity");

  return (
    <Dialog open={open} onOpenChange={setOpen} modal>
      <DialogContent
        className="max-w-screen-md"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <ScrollArea className="pr-3">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="p-2" noValidate id="business-activity-dialog-form">
              <DialogHeader>
                <p>Tax date to which this activity applies</p>
              </DialogHeader>
              <div className="flex-col space-y-2 pt-4">
                <FormField
                  control={form.control}
                  name="from"
                  render={({ field, fieldState }) => (
                    <FormItem className="md:w-1/2 sm:w-full">
                      <FormLabel>From *</FormLabel>
                      <FormControl>
                        <DatePicker
                          date={field.value}
                          onChange={field.onChange}
                          invalid={!!fieldState.error}
                          disabledDates={{
                            before: new Date(`${year}-01-01 00:00:00`),
                            after: new Date(`${year}-12-31 00:00:00`),
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="to"
                  render={({ field, fieldState }) => (
                    <FormItem className="md:w-1/2 sm:w-full">
                      <FormLabel>To *</FormLabel>
                      <FormControl>
                        <DatePicker
                          date={field.value}
                          onChange={field.onChange}
                          invalid={!!fieldState.error}
                          disabledDates={{
                            before: new Date(`${year}-01-01 00:00:00`),
                            after: new Date(`${year}-12-31 00:00:00`),
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="type"
                  render={({ field, fieldState }) => (
                    <FormItem className="space-y-3">
                      <FormLabel>Primary or secondary *</FormLabel>
                      <FormControl>
                        <RadioGroup
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          invalid={!!fieldState.error}
                          className="flex flex-row space-x-2"
                        >
                          <FormItem className="flex items-center space-x-2 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="Primary" />
                            </FormControl>
                            <FormLabel className="font-normal">
                              Primary
                            </FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-2 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="Secondary" />
                            </FormControl>
                            <FormLabel className="font-normal">
                              Secondary
                            </FormLabel>
                          </FormItem>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="activity"
                  render={({ field, fieldState }) => (
                    <FormItem className="md:w-1/2 sm:w-full">
                      <FormLabel>Business Activity *</FormLabel>
                      <FormControl>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          disabled={field.disabled}
                        >
                          <FormControl>
                            <SelectTrigger invalid={!!fieldState.error}>
                              <SelectValue placeholder="Select activity" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            { Object.values(BusinessActivity).map(activity => (
                              <SelectItem key={activity} value={activity}>
                                {activity}
                              </SelectItem>
                            )) }
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {watchActivity === BusinessActivity.OTHER && (
                  <FormField
                    control={form.control}
                    name="otherActivity"
                    render={({ field, fieldState }) => (
                      <FormItem className="md:w-1/2 sm:w-full">
                        <FormLabel>Please specify other activity *</FormLabel>
                        <FormControl>
                          <Input
                            invalid={!!fieldState.error}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </div>
              <DialogFooter className="pt-4">
                <div className={`flex w-full ${businessActivity && onDelete ? "justify-between" : "justify-end"}`}>
                  {businessActivity && onDelete && (
                    <Button size="sm" variant="destructive" onClick={onDelete} type="button">Delete</Button>
                  )}
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline" onClick={() => setOpen(false)} type="button">Cancel</Button>
                    <Button size="sm" variant="default" type="submit" form="business-activity-dialog-form">Save Business Activity</Button>
                  </div>
                </div>
              </DialogFooter>
            </form>
          </Form>
          <ScrollBar />
        </ScrollArea>
      </DialogContent>
    </Dialog>
  )
}
