import type { ActionFunctionArgs, TypedResponse } from "@remix-run/node";
import { json, redirect } from "@remix-run/node";
import { format } from "date-fns";
import { getCurrentStep, getNextStep } from "../hooks/use-form-steps";
import type { FinancialPeriodSchemaType } from "../types/financial-period-schema";
import { Pages } from "./form-pages";
import { commitSession, getSession } from "~/lib/auth/utils/session.server";
import { getFlattenedSubmission, getUnflattenedDataSet } from "~/lib/submission/utilities/submission-data-set-auto";
import { middleware } from "~/lib/middlewares.server";
import { SubmissionStatusNames } from "~/lib/submission/utilities/submission-status";
import { clientGetSubmission, clientPutSubmissionDataSet, clientSubmitSubmission, clientUpdateSubmissionInformation } from "~/services/api-generated";
import { authHeaders } from "~/lib/auth/utils/auth-headers";

export async function getFormAction({ request, params }: ActionFunctionArgs, page: string): Promise<TypedResponse<null> | undefined> {
  await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany", "requireBfrModule"], request);
  const session = await getSession(request.headers.get("Cookie"));
  const { id } = params;

  if (!id) {
    throw new Error("Submission ID is required");
  }

  const formData = await request.formData();
  const data = JSON.parse(formData.get("data") as string);
  const { data: submission } = await clientGetSubmission({
    headers: await authHeaders(request),
    path: { submissionId: id },
    query: { includeFormDocument: true },
  });

  if (!submission) {
    throw new Error("Submission not found")
  }

  const currentStep = getCurrentStep(page);
  if (!currentStep) {
    throw new Error("Current step is not available");
  }

  const unflattenedData = getUnflattenedDataSet(submission);
  // Update the submission dataset with the new data but keep the other pages intact
  const unflattenedNewSubmission = {
    ...unflattenedData,
    [page]: {
      ...unflattenedData[page],
      ...data,
    },
  };
  const newSubmissionData = getFlattenedSubmission(unflattenedNewSubmission);

  try {
    await clientPutSubmissionDataSet({ headers: await authHeaders(request), path: { submissionId: id }, body: { id, dataSet: newSubmissionData, documentIds: submission.documentIds } })
    if (page === Pages.FINANCIAL_PERIOD && (submission.status === SubmissionStatusNames.Temporal || submission.status === SubmissionStatusNames.Draft)) {
      const { startFiscalYear, endFiscalYear } = unflattenedNewSubmission[Pages.FINANCIAL_PERIOD] as FinancialPeriodSchemaType
      if (!startFiscalYear || !endFiscalYear) {
        throw new Error("Start date and end date are mandatory for update the submission information")
      }

      const { error } = await clientUpdateSubmissionInformation({ headers: await authHeaders(request), path: { submissionId: id }, body: { startAt: format(startFiscalYear, "yyyy-MM-dd"), endAt: format(endFiscalYear, "yyyy-MM-dd") } })

      if (error) {
        session.flash("notification", { title: "An Error has occurred", message: error.exceptionMessage, variant: "error" });

        return json(null, { status: 500, headers: { "Set-Cookie": await commitSession(session) } });
      }
    }

    if (page === Pages.FINALIZE) {
      if (submission.statusText !== SubmissionStatusNames.Draft && submission.statusText !== SubmissionStatusNames.Revision && submission.statusText !== SubmissionStatusNames.Temporal) {
        session.flash("notification", { title: "Error!", message: "Submission was already submitted", variant: "error" });

        return redirect("/basic-financial-report/new", { headers: { "Set-Cookie": await commitSession(session) } });
      }

      // Finalize the submission
      const { error } = await clientSubmitSubmission({ headers: await authHeaders(request), path: { submissionId: id }, body: { submissionId: id } });
      if (error) {
        session.flash("notification", { title: "An Error has occurred", message: error.exceptionMessage, variant: "error" });

        return json(null, { status: 500, headers: { "Set-Cookie": await commitSession(session) } });
      }

      return redirect(`/basic-financial-report/${params.id}/confirmation`);
    }

    const nextPage = getNextStep(unflattenedNewSubmission, page);

    if (nextPage) {
      return redirect(`/basic-financial-report/${params.id}/${nextPage}`);
    }
  } catch (error) {
    if (error instanceof Response) {
      const errorMessage = await error.text();

      if (errorMessage) {
        session.flash("notification", { title: "An Error has occurred", message: errorMessage, variant: "error" });

        return json(null, { status: error.status, headers: { "Set-Cookie": await commitSession(session) } });
      }
    } else {
      throw error;
    }
  }
}
