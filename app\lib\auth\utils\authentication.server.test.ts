import {
  type Mock,
  afterEach,
  beforeEach,
  describe,
  expect,
  it,
  vi,
} from "vitest";
import type { AuthenticationResult } from "@azure/msal-node";
import { LogLevel } from "@azure/msal-node";
import { getAccessTokenByClientCredentials } from "@netpro/auth";
import type { ConfigurationArgs } from "./authentication.server";
import { createConfiguration, getClientCredentialsToken } from "./authentication.server";

// Mock the @netpro/auth module
vi.mock("@netpro/auth", async importOriginal => ({
  ...await importOriginal<typeof import("@netpro/auth")>(),
  getAccessTokenByClientCredentials: vi.fn(),
}));

// Mock the sessionStorage
vi.mock("~/lib/auth/utils/session.server", () => ({
  sessionStorage: {
    getSession: vi.fn(),
    commitSession: vi.fn(),
  },
}));

describe("authentication.server.ts", () => {
  describe("createConfiguration", () => {
    const validArgs: ConfigurationArgs = {
      clientId: "test-client-id",
      clientSecret: "test-client-secret",
      tenantSubdomain: "test-tenant",
      nodeEnv: "development",
    };

    it("should create a valid configuration with correct arguments", () => {
      const config = createConfiguration(validArgs);

      expect(config).toEqual({
        auth: {
          clientId: "test-client-id",
          clientSecret: "test-client-secret",
          authority: "https://test-tenant.ciamlogin.com/",
        },
        system: {
          loggerOptions: {
            loggerCallback: expect.any(Function),
            piiLoggingEnabled: false,
            logLevel: LogLevel.Verbose,
          },
        },
      });
    });

    it("should set logLevel to Warning in production environment", () => {
      const prodArgs = { ...validArgs, nodeEnv: "production" };
      const config = createConfiguration(prodArgs);

      expect(config?.system?.loggerOptions?.logLevel).toBe(LogLevel.Warning);
    });

    it("should set logLevel to Verbose in non-production environment", () => {
      const devArgs = { ...validArgs, nodeEnv: "development" };
      const config = createConfiguration(devArgs);

      expect(config?.system?.loggerOptions?.logLevel).toBe(LogLevel.Verbose);
    });

    it("should include additional options in the system object", () => {
      const argsWithOptions = {
        ...validArgs,
        options: {
          cache: {
            claimsBasedCachingEnabled: false,
          },
        },
      };
      const config = createConfiguration(argsWithOptions);

      expect(config.system).toHaveProperty("cache", {
        claimsBasedCachingEnabled: false,
      });
    });

    it("should throw an error if required arguments are missing", () => {
      const invalidArgs = { ...validArgs };
      // @ts-expect-error We are testing the error handling, so we need to delete required arguments
      delete invalidArgs.clientId;

      expect(() => {
        createConfiguration(invalidArgs);
      }).toThrow();
    });

    it("should call console.log in the loggerCallback", () => {
      const consoleSpy = vi.spyOn(console, "log").mockImplementation(() => {});
      const config = createConfiguration(validArgs);
      const logLevel = 2; // Example log level
      const message = "Test log message";
      const containsPii = false;

      // @ts-expect-error We are testing the loggerCallback function, which might not be defined and will fail the test
      config.system.loggerOptions.loggerCallback(logLevel, message, containsPii);

      expect(consoleSpy).toHaveBeenCalledWith(message, { logLevel, containsPii });

      consoleSpy.mockRestore();
    });
  });

  describe("getClientCredentialsToken", () => {
    const mockScope = "mock-scope";
    const mockAuthResult: Partial<AuthenticationResult> = {
      accessToken: "mock-access-token",
      expiresOn: new Date(),
      extExpiresOn: new Date(),
      idToken: "mock-id-token",
      account: null,
      fromCache: false,
      scopes: [mockScope],
      tokenType: "Bearer",
    };

    beforeEach(() => {
      // Set up the mock environment variable
      vi.stubEnv("ENTRA_API_SCOPE", mockScope);
    });

    afterEach(() => {
      vi.unstubAllEnvs();
      vi.resetAllMocks();
    });

    it("should call getAccessTokenByClientCredentials with correct parameters", async () => {
      // Set up the mock to return a successful result
      (getAccessTokenByClientCredentials as Mock).mockResolvedValue(mockAuthResult);

      // Call the function
      const result = await getClientCredentialsToken();

      // Check that getAccessTokenByClientCredentials was called with the correct parameters
      expect(getAccessTokenByClientCredentials).toHaveBeenCalledWith({
        scopes: [mockScope],
        clientApplication: expect.anything(), // We can't easily mock the clientApplication, so we just check if it's present
      });

      // Check that the function returns the expected result
      expect(result).toEqual(mockAuthResult);
    });

    it("should throw an error if getAccessTokenByClientCredentials fails", async () => {
      // Set up the mock to throw an error
      const mockError = new Error("Failed to get token");
      (getAccessTokenByClientCredentials as Mock).mockRejectedValue(mockError);

      // Call the function and expect it to throw
      await expect(getClientCredentialsToken()).rejects.toThrow("Failed to get token");
    });

    it("should use the ENTRA_API_SCOPE environment variable", async () => {
      // Set up the mock to return a successful result
      (getAccessTokenByClientCredentials as Mock).mockResolvedValue(mockAuthResult);

      // Call the function
      await getClientCredentialsToken();

      // Check that getAccessTokenByClientCredentials was called with the correct scope
      expect(getAccessTokenByClientCredentials).toHaveBeenCalledWith(
        expect.objectContaining({
          scopes: [mockScope],
        }),
      );
    });
  });
});
