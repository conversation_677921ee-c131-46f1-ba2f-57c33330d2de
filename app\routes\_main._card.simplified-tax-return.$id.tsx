import type { JS<PERSON> } from "react";
import { Outlet, useLoaderData } from "@remix-run/react";
import type { LoaderFunctionArgs, TypedResponse } from "@remix-run/node";
import { redirect } from "@remix-run/node";
import { SubmissionProvider } from "~/lib/submission/context/submission-context";
import type { Submission } from "~/features/submissions/api/get-submission";
import { getSubmission } from "~/features/submissions/api/get-submission";
import { middleware } from "~/lib/middlewares.server";
import { getUnflattenedDataSet } from "~/lib/submission/utilities/submission-data-set";
import { getFirstStep, useFormSteps } from "~/lib/simplified-tax-return/hooks/use-form-steps";
import { FormLayout } from "~/features/simplified-tax-return/components/forms/FormLayout";
import { SubmissionStatusNames } from "~/lib/submission/utilities/submission-status";
import { commitSession, getSession } from "~/lib/auth/utils/session.server";

export async function loader({ request, params }: LoaderFunctionArgs): Promise<TypedResponse<never> | {
  submissionData: Record<string, any>
  financialYear: Submission["financialYear"]
}> {
  const { userId, accessToken, company } = await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany", "requireStrModule"], request);
  const { id } = params;
  const session = await getSession(request.headers.get("Cookie"));
  if (!id) {
    throw new Error("Submission ID is required");
  }

  if (!company.isActive) {
    session.flash("notification", {
      variant: "error",
      title: "Company is closed",
      message: "Your company status is closed, please contact your Trident Officer if you believe this is incorrect.",
    });

    return redirect("/simplified-tax-return/drafts", { headers: { "Set-Cookie": await commitSession(session) } });
  }

  const submission = await getSubmission({
    id,
    accessToken,
    userId,
    query: { includeFormDocument: true },
  });

  if (!params.pageName) {
    if (submission.statusText === SubmissionStatusNames.Draft || submission.statusText === SubmissionStatusNames.Revision) {
      const firstStep = getFirstStep(submission.financialYear);
      // Redirect to the first page of the form
      throw redirect(`/simplified-tax-return/${id}/${firstStep.page}`);
    }
  }

  const submissionData = getUnflattenedDataSet(submission);

  return {
    submissionData,
    financialYear: submission.financialYear,
  }
}

export default function SimplifiedTaxReturnFormLayout(): JSX.Element {
  const { submissionData, financialYear } = useLoaderData<typeof loader>();
  const formSteps = useFormSteps(financialYear);

  return (
    <SubmissionProvider submissionData={submissionData} financialYear={financialYear}>
      <FormLayout formSteps={formSteps}>
        <Outlet />
      </FormLayout>
    </SubmissionProvider>
  )
}
