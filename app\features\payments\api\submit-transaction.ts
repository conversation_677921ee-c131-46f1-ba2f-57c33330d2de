import { z } from "zod";
import { client } from "~/lib/api-client";
import type { ClientRequestHeaders } from "~/lib/types/client-request-headers";

export const submitTransactionSchema = z.object({
  transactionId: z.string().min(1, "Required"),
  tokenId: z.string().min(1, "Required"),
});

export type SubmitTransactionType = z.infer<typeof submitTransactionSchema>;

export type SubmittedTransaction = {
  transactionId: string
  providerTransactionId: string
  result: number
  resultText: string
  resultNumber: number
}

export function submitTransaction({ paymentId, data, accessToken, userId }:
  { paymentId: string, data: SubmitTransactionType } & ClientRequestHeaders): Promise<SubmittedTransaction> {
  return client.post(
    `/client/payments/${paymentId}/transactions/${data.transactionId}/submit`,
    accessToken,
    userId,
    data,
  );
}
