import type { ReactNode } from "react";
import type { FormStep } from "../hooks/use-form-steps";
import type { FinancialPeriodSchemaType } from "../types/financial-period-schema";
import { financialPeriodSchema } from "../types/financial-period-schema";
import { equityDetailsSchema } from "../types/equity-details-schema";
import { incomeDetailsSchema } from "../types/income-details-schema";
import { expenseDetailsSchema } from "../types/expense-details-schema";
import { liabilitiesDetailsSchema } from "../types/liabilities-details-schema";
import { nonCurrentAssetsDetailsSchema } from "../types/non-current-assets-details-schema";
import { currentAssetsDetailsSchema } from "../types/current-assets-details-schema";
import { financialReportsSchema } from "../types/financial-reports-details-schema";
import { finalizeSchema } from "../types/finalize-schema";
import { Pages } from "./form-pages";
import { FinancialPeriod } from "~/components/basic-financial-report/components/forms/financial-period/FinancialPeriod";
import { EquityDetails } from "~/components/basic-financial-report/components/forms/equity-details/EquityDetails";
import { IncomeDetails } from "~/components/basic-financial-report/components/forms/income-details/IncomeDetails";
import { ExpenseDetails } from "~/components/basic-financial-report/components/forms/expense-details/ExpenseDetails";
import { LiabilitiesDetails } from "~/components/basic-financial-report/components/forms/liabilities-details/LiabilitiesDetails";
import { NonCurrentAssetsDetails } from "~/components/basic-financial-report/components/forms/non-current-assets-details/NonCurrentAssetsDetails";
import { CurrentAssetsDetails } from "~/components/basic-financial-report/components/forms/current-assets-details/CurrentAssetsDetails";
import { FinancialReportsDetails } from "~/components/basic-financial-report/components/forms/financial-reports-details/FinancialReportsDetails";
import { Finalize } from "~/components/basic-financial-report/components/forms/finalize/Finalize";

export const formSteps: FormStep[] = [
  {
    name: "Financial period",
    page: Pages.FINANCIAL_PERIOD,
    component: (): ReactNode => <FinancialPeriod />,
    validationSchema: financialPeriodSchema,
    previousPage: null,
    nextPage: (submission: Record<string, unknown>): string | null => {
      const { tridentAccountingRecordsTool } = submission[Pages.FINANCIAL_PERIOD] as FinancialPeriodSchemaType

      if (tridentAccountingRecordsTool === "true") {
        return Pages.EQUITY_DETAILS
      }

      if (tridentAccountingRecordsTool === "false") {
        return Pages.FINALIZE
      }

      return null
    },
  },
  {
    name: "Equity details",
    page: Pages.EQUITY_DETAILS,
    component: (): ReactNode => (
      <EquityDetails />
    ),
    validationSchema: equityDetailsSchema,
    previousPage: Pages.FINANCIAL_PERIOD,
    nextPage: Pages.INCOME_DETAILS,
  },
  {
    name: "Income details",
    page: Pages.INCOME_DETAILS,
    component: (): ReactNode => <IncomeDetails />,
    validationSchema: incomeDetailsSchema,
    previousPage: Pages.EQUITY_DETAILS,
    nextPage: Pages.EXPENSE_DETAILS,
  },
  {
    name: "Expense details",
    page: Pages.EXPENSE_DETAILS,
    component: (): ReactNode => <ExpenseDetails />,
    validationSchema: expenseDetailsSchema,
    previousPage: Pages.INCOME_DETAILS,
    nextPage: Pages.LIABILITIES_DETAILS,
  },
  {
    name: "Liabilities details",
    page: Pages.LIABILITIES_DETAILS,
    component: (): ReactNode => <LiabilitiesDetails />,
    validationSchema: liabilitiesDetailsSchema,
    previousPage: Pages.EXPENSE_DETAILS,
    nextPage: Pages.NON_CURRENT_ASSETS_DETAILS,
  },
  {
    name: "Non-Current Assets details",
    page: Pages.NON_CURRENT_ASSETS_DETAILS,
    component: (): ReactNode => <NonCurrentAssetsDetails />,
    validationSchema: nonCurrentAssetsDetailsSchema,
    previousPage: Pages.LIABILITIES_DETAILS,
    nextPage: Pages.CURRENT_ASSETS_DETAILS,
  },
  {
    name: "Current Assets details",
    page: Pages.CURRENT_ASSETS_DETAILS,
    component: (): ReactNode => <CurrentAssetsDetails />,
    validationSchema: currentAssetsDetailsSchema,
    previousPage: Pages.NON_CURRENT_ASSETS_DETAILS,
    nextPage: Pages.FINANCIAL_REPORTS_DETAILS,
  },
  {
    name: "Financial Reports details",
    page: Pages.FINANCIAL_REPORTS_DETAILS,
    component: (): ReactNode => <FinancialReportsDetails />,
    validationSchema: financialReportsSchema,
    previousPage: Pages.CURRENT_ASSETS_DETAILS,
    nextPage: Pages.FINALIZE,
  },
  {
    name: "Declaration",
    page: Pages.FINALIZE,
    component: (): ReactNode => <Finalize />,
    validationSchema: finalizeSchema,
    previousPage: (submission: Record<string, unknown>): string | null => {
      const { tridentAccountingRecordsTool } = submission[Pages.FINANCIAL_PERIOD] as FinancialPeriodSchemaType

      if (tridentAccountingRecordsTool === "true") {
        return Pages.FINANCIAL_REPORTS_DETAILS
      }

      if (tridentAccountingRecordsTool === "false") {
        return Pages.FINANCIAL_PERIOD
      }

      return null
    },
    nextPage: Pages.CONFIRMATION,
  },

];
